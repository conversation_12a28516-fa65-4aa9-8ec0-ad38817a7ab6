# Use multi-stage build for smaller final image
FROM python:3.9-slim-bullseye AS builder

# Set working directory
WORKDIR /app

# Install build dependencies in smaller batches
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    gcc \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt && \
    pip install --user --no-cache-dir gunicorn>=21.0.0

# Final stage
FROM python:3.9-slim-bullseye

# Install runtime dependencies only
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Set working directory
WORKDIR /app

# Create non-root user
RUN useradd -m -u 1000 appuser

# Copy Python packages from builder to appuser
COPY --from=builder --chown=appuser:appuser /root/.local /home/<USER>/.local

# Copy source code
COPY --chown=appuser:appuser src/ ./src/
COPY --chown=appuser:appuser wsgi.py .
COPY --chown=appuser:appuser run_flask.py .
COPY --chown=appuser:appuser .env.example .

# Copy legacy script for backward compatibility
COPY --chown=appuser:appuser run_reports.py .

# Create reports directory
RUN mkdir -p reports && chown -R appuser:appuser reports

# Switch to non-root user
USER appuser

# Make sure scripts in .local are usable
ENV PATH=/home/<USER>/.local/bin:$PATH

# Set environment variables
ENV PYTHONPATH=/app
ENV FLASK_ENV=production
ENV PORT=8000
ENV HOST=0.0.0.0

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command runs Flask API with gunicorn
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--timeout", "120", "--keep-alive", "2", "--max-requests", "1000", "--max-requests-jitter", "100", "wsgi:app"]