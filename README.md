# Marine HMM Reports API

A Flask-based REST API for generating and managing Marine Hull & Machinery (HMM) risk reports with automated scheduling capabilities.

## Architecture Overview

The application follows a layered architecture pattern with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    API Layer (Controllers)                  │
├─────────────────────────────────────────────────────────────┤
│                  Business Logic (Managers)                  │
├─────────────────────────────────────────────────────────────┤
│                    Services Layer                           │
├─────────────────────────────────────────────────────────────┤
│                 Data Access Layer (DAL)                     │
├─────────────────────────────────────────────────────────────┤
│              External Services (PostgreSQL, BigQuery,       │
│                    GCS, Pub/Sub)                           │
└─────────────────────────────────────────────────────────────┘
```

### Key Components

- **Controllers**: REST API endpoints for report generation and health checks
- **Managers**: Business logic orchestration (ReportManager, SchedulerManager)
- **Services**: Reusable service components (ExcelService, StorageService, NotificationService)
- **DAL**: Data access layer for PostgreSQL and BigQuery
- **Utils**: Utilities for job tracking, validation, and logging

## Features

- ✅ **Async Report Generation**: Generate reports asynchronously with job tracking
- ✅ **Scheduled Reports**: Automated monthly report generation (7th of each month, 8 AM UTC)
- ✅ **Multi-tenant Support**: Generate reports for multiple tenants
- ✅ **Cloud Storage**: Automatic upload to Google Cloud Storage
- ✅ **Notifications**: Pub/Sub notifications for report completion
- ✅ **Health Monitoring**: Comprehensive health checks for all services
- ✅ **Input Validation**: Request validation and error handling
- ✅ **Structured Logging**: Request correlation and structured logging
- ✅ **Production Ready**: Docker support with gunicorn WSGI server

## API Endpoints

### Report Management
- `POST /api/reports/generate` - Generate report for a tenant
- `GET /api/reports/status/<job_id>` - Get job status
- `GET /api/reports/jobs` - List jobs with filtering
- `POST /api/reports/jobs/<job_id>/cancel` - Cancel a running job

### Health Monitoring
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed service health
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe

## Quick Start

### Using Docker (Recommended)

1. **Build and run with Docker Compose:**
```bash
docker-compose up --build
```

2. **Test the API:**
```bash
# Health check
curl http://localhost:8000/health

# Generate report (replace with valid tenant_id)
curl -X POST http://localhost:8000/api/reports/generate \
  -H "Content-Type: application/json" \
  -d '{"tenant_id": "your-tenant-id"}'
```

### Local Development

1. **Install dependencies:**
```bash
pip install -r requirements.txt
```

2. **Set environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Run the development server:**
```bash
python run_flask.py
```

## Configuration

### Environment Variables

#### Flask Configuration
- `FLASK_ENV`: Environment (development, staging, production)
- `SECRET_KEY`: Flask secret key (required in production)
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)
- `PORT`: Server port (default: 8000)
- `HOST`: Server host (default: 0.0.0.0)

#### Scheduler Configuration
- `SCHEDULER_DAY`: Day of month for reports (default: 7)
- `SCHEDULER_HOUR`: Hour in UTC (default: 8)
- `SCHEDULER_MINUTE`: Minute (default: 0)
- `SCHEDULER_TIMEZONE`: Timezone (default: UTC)
- `SCHEDULER_ENABLED`: Enable/disable scheduler (default: true)

#### Database Configuration
- `POSTGRES_HOST`: PostgreSQL host
- `POSTGRES_PORT`: PostgreSQL port (default: 5432)
- `POSTGRES_USERNAME`: PostgreSQL username
- `POSTGRES_PASSWORD`: PostgreSQL password
- `POSTGRES_DB`: PostgreSQL database name

#### GCP Configuration
- `PROJECT_ID`: GCP project ID
- `HMM_REPORTS_BUCKET_NAME`: GCS bucket for reports
- `PUBSUB_TOPIC`: Pub/Sub topic for notifications

#### Multi-tenant Configuration
- `ENABLED_FOR_TENANT_IDS`: Comma-separated tenant IDs
- `TENANT_USERS_CONFIG`: Tenant user mapping with optional tenant names
  - New format: `"tenant_id:tenant_name|Name1:email1,Name2:email2;tenant_id2:tenant_name2|Name3:email3"`
  - Legacy format: `"tenant_id|Name1:email1,Name2:email2;tenant_id2|Name3:email3"` (still supported)
  - When tenant names are provided, they will be used in Excel filenames instead of tenant IDs

## Project Structure

```
├── src/
│   ├── app.py                      # Flask application factory
│   ├── config/
│   │   ├── settings.py             # Environment-based configuration
│   │   └── scheduler_config.py     # Scheduler configuration
│   ├── controllers/
│   │   ├── health_controller.py    # Health check endpoints
│   │   └── report_controller.py    # Report API endpoints
│   ├── managers/
│   │   ├── report_manager.py       # Report generation orchestration
│   │   └── scheduler_manager.py    # APScheduler integration
│   ├── services/
│   │   ├── excel_service.py        # Excel report generation
│   │   ├── storage_service.py      # GCS storage operations
│   │   └── notification_service.py # Pub/Sub notifications
│   ├── dal/
│   │   ├── database_connector.py   # PostgreSQL connector
│   │   └── bigquery_connector.py   # BigQuery connector
│   └── utils/
│       ├── job_tracker.py          # In-memory job tracking
│       ├── validators.py           # Input validation
│       └── logging_config.py       # Logging configuration
├── wsgi.py                         # WSGI entry point
├── run_flask.py                    # Development server
├── docker-compose.yml              # Docker Compose configuration
├── Dockerfile                      # Docker configuration
└── requirements.txt                # Python dependencies
```

## Development

### Running Tests

```bash
# Install test dependencies
pip install pytest pytest-flask pytest-cov

# Run tests
pytest

# Run with coverage
pytest --cov=src
```

### Code Quality

```bash
# Install development tools
pip install black flake8 mypy

# Format code
black src/

# Lint code
flake8 src/

# Type checking
mypy src/
```

### Adding New Features

1. **Controllers**: Add new API endpoints in `src/controllers/`
2. **Business Logic**: Add orchestration logic in `src/managers/`
3. **Services**: Add reusable services in `src/services/`
4. **Data Access**: Add database operations in `src/dal/`
5. **Utilities**: Add helper functions in `src/utils/`

## Deployment

### Production Deployment

1. **Build Docker image:**
```bash
docker build -t marine-hmm-api .
```

2. **Run with production settings:**
```bash
docker run -p 8000:8000 \
  -e FLASK_ENV=production \
  -e SECRET_KEY=your-secret-key \
  -e POSTGRES_HOST=your-db-host \
  -e PROJECT_ID=your-gcp-project \
  marine-hmm-api
```

### Kubernetes Deployment

The application includes health check endpoints for Kubernetes:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: marine-hmm-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: marine-hmm-api
  template:
    metadata:
      labels:
        app: marine-hmm-api
    spec:
      containers:
      - name: api
        image: marine-hmm-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: FLASK_ENV
          value: "production"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Monitoring and Logging

### Structured Logging

The application uses structured logging with request correlation:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "marine_hmm_api.report_manager",
  "message": "Report generation completed",
  "request_id": "abc12345",
  "tenant_id": "tenant1",
  "job_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### Health Monitoring

Monitor application health using the health endpoints:

- `/health` - Basic health status
- `/health/detailed` - Service dependency health
- `/health/ready` - Readiness for traffic
- `/health/live` - Application liveness

### Metrics

Key metrics to monitor:

- Report generation success/failure rates
- Job queue length and processing times
- Database connection health
- GCS upload success rates
- Pub/Sub message delivery rates

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Check PostgreSQL connection parameters
   - Verify network connectivity
   - Check database credentials

2. **GCS Upload Failures**
   - Verify GCP credentials and permissions
   - Check bucket existence and permissions
   - Monitor GCS quotas and limits

3. **Scheduler Not Running**
   - Check `SCHEDULER_ENABLED` environment variable
   - Verify scheduler configuration parameters
   - Check application logs for scheduler errors

4. **Report Generation Failures**
   - Check tenant configuration
   - Verify data availability in PostgreSQL/BigQuery
   - Monitor job status through API endpoints

### Debugging

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
export FLASK_ENV=development
```

Check job status:

```bash
curl http://localhost:8000/api/reports/jobs?status=failed
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and ensure they pass
6. Submit a pull request

## License

This project is proprietary software. All rights reserved.

## Support

For support and questions, please contact the development team or create an issue in the project repository.