#!/usr/bin/env python3
"""Development runner for Marine HMM Reports API."""

import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from app import create_app

if __name__ == "__main__":
    # Set development environment
    os.environ.setdefault('FLASK_ENV', 'development')
    
    # Create Flask application
    app = create_app('development')
    
    # Run development server
    port = int(os.getenv('PORT', 5000))
    host = os.getenv('HOST', '127.0.0.1')
    
    print(f"Starting Flask development server on http://{host}:{port}")
    print("Press CTRL+C to quit")
    
    app.run(host=host, port=port, debug=True)