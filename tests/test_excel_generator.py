"""Tests for Excel Report Generator."""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from excel_report_generator import ExcelReportGenerator


class TestExcelReportGenerator:
    """Test cases for ExcelReportGenerator."""
    
    @pytest.fixture
    def mock_postgres_config(self):
        """Mock PostgreSQL configuration."""
        return {
            'host': 'localhost',
            'port': 5432,
            'database': 'test_db',
            'user': 'test_user',
            'password': 'test_pass'
        }
    
    @pytest.fixture
    def mock_bigquery_config(self):
        """Mock BigQuery configuration."""
        return {
            'project_id': 'test-project',
            'credentials_path': None
        }
    
    @pytest.fixture
    def sample_vessel_data(self):
        """Sample vessel data for testing."""
        return pd.DataFrame({
            'imo': [1234567, 2345678],
            'policy_id': ['POL001', 'POL002'],
            'account_id': ['ACC001', 'ACC001'],
            'account_name': ['Test Account', 'Test Account'],
            'vessel_risk_score': [75, 82],
            'expected_loss': [50000, 60000],
            'frequency': [65, 70],
            'severity': [80, 85],
            'vessel_name': ['Test Vessel 1', 'Test Vessel 2']
        })
    
    @patch('excel_report_generator.DatabaseConnector')
    @patch('excel_report_generator.BigQueryConnector')
    def test_init(self, mock_bq_connector, mock_db_connector, mock_postgres_config, mock_bigquery_config):
        """Test ExcelReportGenerator initialization."""
        generator = ExcelReportGenerator(
            postgres_config=mock_postgres_config,
            bigquery_config=mock_bigquery_config,
            tenant_id="test_tenant"
        )
        
        assert generator.tenant_id == "test_tenant"
        assert generator.db_connector is not None
        assert generator.bq_connector is not None
        mock_db_connector.assert_called_once_with(mock_postgres_config)
        mock_bq_connector.assert_called_once_with(mock_bigquery_config)
    
    def test_format_feature_value_with_unit(self, mock_postgres_config, mock_bigquery_config):
        """Test feature value formatting with units."""
        with patch('excel_report_generator.DatabaseConnector'), \
             patch('excel_report_generator.BigQueryConnector'):
            
            generator = ExcelReportGenerator(
                postgres_config=mock_postgres_config,
                bigquery_config=mock_bigquery_config,
                tenant_id="test_tenant"
            )
            
            # Test with units that should be concatenated
            assert generator._format_feature_value_with_unit(123.456, 'tonnes') == '123.46 tonnes'
            assert generator._format_feature_value_with_unit(45.67, 'degrees') == '45.67 degrees'
            
            # Test with units that should NOT be concatenated
            assert generator._format_feature_value_with_unit(1, 'binary') == '1'
            assert generator._format_feature_value_with_unit(123.456, 'number') == '123.46'
            assert generator._format_feature_value_with_unit(5.678, 'index') == '5.68'
            
            # Test edge cases
            assert generator._format_feature_value_with_unit(None, 'tonnes') == '-'
            assert generator._format_feature_value_with_unit('', 'tonnes') == '-'
            assert generator._format_feature_value_with_unit(123.456, None) == '123.46'
            assert generator._format_feature_value_with_unit(123.456, '') == '123.46'
    
    @patch('excel_report_generator.DatabaseConnector')
    @patch('excel_report_generator.BigQueryConnector')
    @patch('os.makedirs')
    def test_generate_reports(self, mock_makedirs, mock_bq_connector, mock_db_connector, 
                             mock_postgres_config, mock_bigquery_config, sample_vessel_data):
        """Test report generation."""
        # Mock the database connectors
        mock_db_instance = Mock()
        mock_bq_instance = Mock()
        mock_db_connector.return_value = mock_db_instance
        mock_bq_connector.return_value = mock_bq_instance
        
        # Mock the data collection methods
        generator = ExcelReportGenerator(
            postgres_config=mock_postgres_config,
            bigquery_config=mock_bigquery_config,
            tenant_id="test_tenant"
        )
        
        with patch.object(generator, '_collect_vessel_data', return_value=sample_vessel_data), \
             patch.object(generator, '_aggregate_account_data', return_value=pd.DataFrame({'Account Name': ['Test Account']})), \
             patch.object(generator, '_generate_combined_excel') as mock_generate_excel:
            
            result = generator.generate_reports(output_dir="test_output")
            
            # Verify the result
            assert result is not None
            assert "Portfolio_HMM_Report_" in result
            assert result.endswith(".xlsx")
            
            # Verify methods were called
            mock_makedirs.assert_called_once_with("test_output", exist_ok=True)
            mock_generate_excel.assert_called_once()


class TestUnitConcatenation:
    """Test cases specifically for unit concatenation logic."""
    
    def test_unit_concatenation_rules(self):
        """Test unit concatenation follows business rules."""
        # Mock the generator
        with patch('excel_report_generator.DatabaseConnector'), \
             patch('excel_report_generator.BigQueryConnector'):
            
            generator = ExcelReportGenerator(
                postgres_config={'host': 'test', 'port': 5432, 'database': 'test', 'user': 'test', 'password': 'test'},
                bigquery_config={'project_id': 'test'},
                tenant_id="test"
            )
            
            # Test cases from the original requirements
            test_cases = [
                # (feature_value, feature_unit, expected_result)
                (123.456, 'tonnes', '123.46 tonnes'),
                (45.67, 'degrees', '45.67 degrees'),
                (89.123, 'rpm', '89.12 rpm'),
                (1000.5, 'GT', '1000.5 GT'),
                (75.8, 'hp', '75.8 hp'),
                (12.345, 'knots', '12.35 knots'),
                (500.789, 'kw', '500.79 kw'),
                (25.4, 'meters', '25.4 meters'),
                (3600.123, 'seconds', '3600.12 seconds'),
                (180.5, 'nm', '180.5 nm'),
                (15.789, 'years', '15.79 years'),
                (85.432, 'pct', '85.43 pct'),
                (50000.123, 'usd', '50000.12 usd'),
                (24.567, 'hours', '24.57 hours'),
                (365.25, 'days', '365.25 days'),
                
                # Units that should NOT be concatenated
                (1, 'binary', '1'),
                (0, 'binary', '0'),
                (123.456, 'char', '123.46'),
                (789.123, 'number', '789.12'),
                (5.678, 'index', '5.68'),
                (42.789, 'value', '42.79'),
                
                # Edge cases
                (None, 'tonnes', '-'),
                ('', 'metres', '-'),
                ('-', 'kg', '-'),
                (123.456, None, '123.46'),
                (123.456, '', '123.46'),
            ]
            
            for feature_value, feature_unit, expected in test_cases:
                result = generator._format_feature_value_with_unit(feature_value, feature_unit)
                assert result == expected, f"Failed for {feature_value}, {feature_unit}: expected {expected}, got {result}"