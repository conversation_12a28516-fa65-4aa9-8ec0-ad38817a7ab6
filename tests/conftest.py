"""
Shared fixtures and configuration for pytest
"""
import os
import pytest
from unittest.mock import Mock, patch
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import tempfile

# Set test environment
os.environ['ENVIRONMENT'] = 'test'
os.environ['CLIENT_NAME'] = 'test_client'


@pytest.fixture
def mock_config():
    """Mock configuration for testing"""
    return {
        'client': {
            'name': 'Test Client',
            'code': 'test'
        },
        'email': {
            'recipients': ['<EMAIL>'],
            'subject_prefix': 'Test Report',
            'from_name': 'Test Reports',
            'from_email': '<EMAIL>',
            'reply_to': '<EMAIL>'
        },
        'report': {
            'delivery_day': 7,
            'delivery_time': '09:00',
            'timezone': 'Europe/London'
        },
        'database': {
            'host': 'test-db',
            'port': 5432,
            'name': 'test_db',
            'schema': 'test_schema',
            'pool_size': 5
        },
        'storage': {
            'bucket_name': 'test-bucket',
            'retention_months': 12
        },
        'aws_ses': {
            'region': 'us-east-1',
            'configuration_set': 'test-config'
        }
    }


@pytest.fixture
def sample_customer_data():
    """Sample customer data from PostgreSQL"""
    return pd.DataFrame({
        'AccountName': ['Account1', 'Account2', 'Account1'],
        'PolicyID': ['POL001', 'POL002', 'POL003'],
        'VesselName': ['Vessel A', 'Vessel B', 'Vessel C'],
        'IMO': ['1234567', '2345678', '3456789']
    })


@pytest.fixture
def sample_hmm_scores():
    """Sample HMM scores data"""
    return pd.DataFrame({
        'vessel_imo': ['1234567', '2345678', '3456789'],
        'risk_score': [0.75, 0.45, 0.82],
        'frequency': [0.12, 0.08, 0.15],
        'severity': [0.63, 0.37, 0.67],
        'expected_loss': [1250.50, 850.25, 1450.75]
    })


@pytest.fixture
def sample_hmm_features():
    """Sample HMM features data"""
    return pd.DataFrame({
        'vessel_imo': ['1234567', '2345678', '3456789'],
        'feature_1': [0.8, 0.6, 0.9],
        'feature_2': [-0.3, 0.2, -0.5],
        'feature_3': [0.5, 0.7, 0.4],
        'shap_feature_1': [0.15, 0.10, 0.18],
        'shap_feature_2': [-0.08, 0.05, -0.12],
        'shap_feature_3': [0.06, 0.08, 0.05]
    })


@pytest.fixture
def sample_inspection_data():
    """Sample inspection data from BigQuery"""
    return pd.DataFrame({
        'imo': ['1234567', '2345678', '3456789'],
        'inspection_date': pd.to_datetime(['2024-01-15', '2024-02-20', '2024-03-10']),
        'defects': [2, 0, 3],
        'detentions': [0, 0, 1]
    })


@pytest.fixture
def mock_gcs_client():
    """Mock Google Cloud Storage client"""
    mock_client = Mock()
    mock_bucket = Mock()
    mock_blob = Mock()
    
    mock_client.bucket.return_value = mock_bucket
    mock_bucket.blob.return_value = mock_blob
    mock_blob.generate_signed_url.return_value = 'https://storage.googleapis.com/test-signed-url'
    
    return mock_client


@pytest.fixture
def mock_ses_client():
    """Mock AWS SES client"""
    mock_client = Mock()
    mock_client.send_email.return_value = {
        'MessageId': 'test-message-id-123',
        'ResponseMetadata': {'HTTPStatusCode': 200}
    }
    mock_client.get_send_quota.return_value = {
        'Max24HourSend': 10000.0,
        'MaxSendRate': 10.0,
        'SentLast24Hours': 100.0
    }
    return mock_client


@pytest.fixture
def mock_bigquery_client():
    """Mock BigQuery client"""
    mock_client = Mock()
    mock_query_job = Mock()
    mock_client.query.return_value = mock_query_job
    mock_query_job.result.return_value = Mock()
    return mock_client


@pytest.fixture
def temp_excel_file():
    """Create a temporary Excel file for testing"""
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        temp_path = f.name
    yield temp_path
    if os.path.exists(temp_path):
        os.unlink(temp_path)


@pytest.fixture
def mock_time():
    """Mock time for testing time-based functions"""
    with patch('time.time') as mock_time:
        mock_time.return_value = 1234567890.0
        yield mock_time


@pytest.fixture(autouse=True)
def reset_environment():
    """Reset environment variables after each test"""
    original_env = os.environ.copy()
    yield
    os.environ.clear()
    os.environ.update(original_env)