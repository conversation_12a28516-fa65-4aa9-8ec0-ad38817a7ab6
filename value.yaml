replicaCount: 1
image:
  repository: ""
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""
ingress:
  enabled: false
  host: test
service:
  type: ClusterIP
  port: 80
  targetport: 8000
container:
  port: 8000

envfrom:
  - configMapRef:
      name: aurora-config-node
  - configMapRef:
      name: marine-hmm-reports-config
  - secretRef:
      name: aurora-admin-secret

resources:
  limits:
    cpu: 900m
    memory: 4Gi
  requests:
    cpu: 100m
    memory: 110Mi

livenessProbe:
  enabled: true
  path: /health
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 10
  successThreshold: 1
  failureThreshold: 2

readinessProbe:
  enabled: true
  path: /health/ready
  initialDelaySeconds: 10
  periodSeconds: 5
  timeoutSeconds: 10 
  successThreshold: 1
  failureThreshold: 2
  
nodeSelector: {}

tolerations: []

affinity: 
   enabled: false