"""WSGI entry point for Marine HMM Reports API."""

import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from app import create_app

# Create Flask application
app = create_app(os.getenv('FLASK_ENV', 'production'))

if __name__ == "__main__":
    # For development server
    port = int(os.getenv('PORT', 5000))
    host = os.getenv('HOST', '0.0.0.0')
    
    app.run(host=host, port=port, debug=app.config.get('DEBUG', False))