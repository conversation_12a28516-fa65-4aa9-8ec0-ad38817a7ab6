version: '3.8'

services:
  marine-hmm-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=development
      - LOG_LEVEL=DEBUG
      - SCHEDULER_ENABLED=true
      - POSTGRES_HOST=localhost
      - POSTGRES_PORT=5432
      - POSTGRES_USERNAME=postgres
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=questmarine
      - PROJECT_ID=prj-nonprod-eng-svc-01
      - HMM_REPORTS_BUCKET_NAME=hull-nonprod-static-content
      - PUBSUB_TOPIC=marine-reports
      - ENABLED_FOR_TENANT_IDS=tenant1,tenant2
      - CORS_ORIGINS=http://localhost:3000,http://localhost:8080
    volumes:
      - ./src:/app/src
      - ./reports:/app/reports
    command: ["python", "run_flask.py"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add PostgreSQL for local development
  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=questmarine
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data: