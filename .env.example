# Aurora Configuration
AURORA_DB_HOST=localhost
AURORA_DB_PORT=5432
AURORA_USERNAME=your_username
AURORA_PASSWORD=your_password
DB_NAME=marine_db

# Flask Configuration
FLASK_ENV=development
LOG_LEVEL=DEBUG

# Scheduler Configuration
SCHEDULER_ENABLED=true
SCHEDULER_TIME=15:10:30    # 15th of month, 10:30 AM UTC
SCHEDULER_TIMEZONE=UTC

# GCP Configuration
PROJECT_ID=prj-nonprod-eng-svc-01
HMM_REPORTS_BUCKET_NAME=gcs-eng-hull-nonprod-reports-01

# Multi-tenant Report Settings
ENABLED_FOR_TENANT_IDS=cea7760d-71ec-4f57-b65d-24655de59a43
PUBSUB_TOPIC=hull-dev-alert-notification-reactor
# Format: tenant_id:tenant_name|user1:email1,user2:email2;tenant_id2:tenant_name2|user3:email3
# Tenant names will be used in Excel filenames instead of tenant IDs
TENANT_USERS_CONFIG=cea7760d-71ec-4f57-b65d-24655de59a43:dev|Nishant Nayan:<EMAIL>,Naveen Negi:<EMAIL>