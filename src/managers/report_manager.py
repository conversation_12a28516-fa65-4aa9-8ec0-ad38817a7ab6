"""Report manager for orchestrating report generation in Marine HMM Reports API."""

import os
import logging
import uuid
import traceback
from datetime import datetime
from typing import Dict, List, Optional
from concurrent.futures import ThreadPoolExecutor
import threading

from services.excel_service import ExcelService
from services.streaming_excel_service import StreamingExcelService
from services.storage_service import StorageService
from services.notification_service import NotificationService
from utils.job_tracker import JobTracker

logger = logging.getLogger(__name__)


class ReportManager:
    """Manager for orchestrating report generation operations."""
    
    def __init__(self, excel_service: ExcelService = None, 
                 storage_service: StorageService = None,
                 notification_service: NotificationService = None,
                 job_tracker: JobTracker = None,
                 use_streaming: bool = True):
        """Initialize report manager with services.
        
        Args:
            excel_service: Excel generation service (ignored if use_streaming=True)
            storage_service: GCS storage service
            notification_service: Pub/Sub notification service
            job_tracker: Job tracking service
            use_streaming: Use memory-optimized streaming service for large datasets
        """
        self.storage_service = storage_service or StorageService()
        self.notification_service = notification_service or NotificationService()
        self.job_tracker = job_tracker or JobTracker()
        self.use_streaming = use_streaming
        
        # Initialize appropriate Excel service
        if use_streaming:
            self.excel_service = StreamingExcelService(
                storage_client=self.storage_service.client,
                bucket_name=self.storage_service.config['bucket_name']
            )
            logger.info("Initialized with StreamingExcelService for memory optimization")
        else:
            self.excel_service = excel_service or ExcelService()
            logger.info("Initialized with standard ExcelService")
        
        # Thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="report-gen")
        
        # Lock for thread-safe operations
        self._lock = threading.Lock()
    
    def generate_report_async(self, tenant_id: str, user_id: str = None) -> str:
        """Start asynchronous report generation for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            user_id: Optional user identifier who requested the report
            
        Returns:
            Job ID for tracking the report generation
        """
        with self._lock:
            logger.info(f"DEDUPLICATION CHECK: Checking for existing running jobs for tenant {tenant_id}")
            
            # Check if there's already a running job for this tenant
            running_jobs = self.job_tracker.list_jobs(
                job_type='report_generation',
                tenant_id=tenant_id,
                status='running',
                limit=10
            )
            
            logger.info(f"DEDUPLICATION RESULT: Found {len(running_jobs)} running jobs for tenant {tenant_id}")
            
            if running_jobs:
                existing_job_id = running_jobs[0]['id']
                logger.info(f"DUPLICATE PREVENTED: Report generation already running for tenant {tenant_id}, returning existing job_id: {existing_job_id}")
                return existing_job_id
            
            job_id = str(uuid.uuid4())
            
            try:
                # Create job record with 'running' status immediately to prevent race conditions
                job_data = {
                    'tenant_id': tenant_id,
                    'user_id': user_id,
                    'created_at': datetime.utcnow(),
                    'started_at': datetime.utcnow(),
                    'type': 'report_generation'
                }
                
                # Create job with 'running' status to prevent duplicate jobs
                logger.info(f"NEW JOB CREATED: Creating job {job_id} for tenant {tenant_id} with 'running' status")
                logger.info(f"CALL STACK: {traceback.format_stack()[-3:-1]}")  # Show last 2 stack frames
                self.job_tracker.create_job(job_id, job_data, status='running')
                
                # Submit async task
                future = self.executor.submit(self._generate_report_task, job_id, tenant_id)
                
                # Store future reference for potential cancellation
                self.job_tracker.update_job_status(job_id, 'running', {
                    'future': future,
                    'started_at': datetime.utcnow()
                })
                
                logger.info(f"Started async report generation for tenant {tenant_id}, job_id: {job_id}")
                return job_id
                
            except Exception as e:
                logger.error(f"Error starting async report generation: {e}")
                self.job_tracker.update_job_status(job_id, 'failed', {
                    'error': str(e),
                    'failed_at': datetime.utcnow()
                })
                raise
    
    def _generate_report_task(self, job_id: str, tenant_id: str):
        """Internal task for generating reports.
        
        Args:
            job_id: Job identifier
            tenant_id: Tenant identifier
        """
        try:
            logger.info(f"Starting report generation task for job {job_id}, tenant {tenant_id}")
            
            # Update job status
            self.job_tracker.update_job_status(job_id, 'running', {
                'stage': 'excel_generation',
                'stage_started_at': datetime.utcnow()
            })
            
            # Generate filename
            filename = self.storage_service.generate_report_filename(tenant_id)
            
            if self.use_streaming:
                # Generate Excel report directly to GCS (memory optimized)
                gcs_path = self.excel_service.generate_excel_to_gcs(tenant_id, filename)
            else:
                # Traditional approach: generate locally then upload
                excel_path = self.excel_service.generate_excel_report(tenant_id)
                
                # Update job status
                self.job_tracker.update_job_status(job_id, 'running', {
                    'stage': 'file_upload',
                    'stage_started_at': datetime.utcnow(),
                    'excel_path': excel_path
                })
                
                # Upload to GCS
                gcs_path = self.storage_service.upload_file(
                    excel_path, 
                    filename,
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                
                # Clean up local file
                if os.path.exists(excel_path):
                    os.unlink(excel_path)
            
            # Update job status
            self.job_tracker.update_job_status(job_id, 'running', {
                'stage': 'notification',
                'stage_started_at': datetime.utcnow(),
                'gcs_path': gcs_path,
                'filename': filename
            })
            
            # Send notifications
            message_ids = self.notification_service.publish_report_notification(
                tenant_id, gcs_path, filename
            )
            
            # Mark job as completed
            completion_data = {
                'gcs_path': gcs_path,
                'filename': filename,
                'message_ids': message_ids,
                'completed_at': datetime.utcnow()
            }
            
            self.job_tracker.update_job_status(job_id, 'completed', completion_data)
            
            logger.info(f"Report generation completed successfully for job {job_id}")
            
        except Exception as e:
            logger.error(f"Error in report generation task {job_id}: {e}")
            
            # Mark job as failed
            self.job_tracker.update_job_status(job_id, 'failed', {
                'error': str(e),
                'failed_at': datetime.utcnow()
            })
            
            # Send error notification
            try:
                self.notification_service.publish_error_notification(
                    'report_generation_failed',
                    str(e),
                    {'job_id': job_id, 'tenant_id': tenant_id}
                )
            except Exception as notify_error:
                logger.error(f"Failed to send error notification: {notify_error}")
            
            raise
    
    def generate_report_sync(self, tenant_id: str) -> Dict:
        """Generate report synchronously.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary with report generation results
        """
        try:
            logger.info(f"Starting synchronous report generation for tenant: {tenant_id}")
            
            # Generate filename
            filename = self.storage_service.generate_report_filename(tenant_id)
            
            if self.use_streaming:
                # Generate Excel report directly to GCS (memory optimized)
                gcs_path = self.excel_service.generate_excel_to_gcs(tenant_id, filename)
            else:
                # Traditional approach: generate locally then upload
                excel_path = self.excel_service.generate_excel_report(tenant_id)
                
                # Upload to GCS
                gcs_path = self.storage_service.upload_file(
                    excel_path, 
                    filename,
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                
                # Clean up local file
                if os.path.exists(excel_path):
                    os.unlink(excel_path)
            
            # Send notifications
            message_ids = self.notification_service.publish_report_notification(
                tenant_id, gcs_path, filename
            )
            
            result = {
                'status': 'completed',
                'gcs_path': gcs_path,
                'filename': filename,
                'message_ids': message_ids,
                'generated_at': datetime.utcnow().isoformat() + 'Z'
            }
            
            logger.info(f"Synchronous report generation completed for tenant: {tenant_id}")
            return result
            
        except Exception as e:
            logger.error(f"Error in synchronous report generation: {e}")
            
            # Send error notification
            try:
                self.notification_service.publish_error_notification(
                    'report_generation_failed',
                    str(e),
                    {'tenant_id': tenant_id}
                )
            except Exception as notify_error:
                logger.error(f"Failed to send error notification: {notify_error}")
            
            raise
    
    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get status of a report generation job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job status dictionary or None if not found
        """
        return self.job_tracker.get_job_status(job_id)
    
    def list_jobs(self, tenant_id: str = None, status: str = None, 
                  limit: int = 50) -> List[Dict]:
        """List report generation jobs.
        
        Args:
            tenant_id: Optional tenant filter
            status: Optional status filter
            limit: Maximum number of jobs to return
            
        Returns:
            List of job dictionaries
        """
        return self.job_tracker.list_jobs(
            job_type='report_generation',
            tenant_id=tenant_id,
            status=status,
            limit=limit
        )
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a running report generation job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if cancellation successful, False otherwise
        """
        try:
            job_status = self.job_tracker.get_job_status(job_id)
            
            if not job_status:
                logger.warning(f"Job not found for cancellation: {job_id}")
                return False
            
            if job_status['status'] not in ['pending', 'running']:
                logger.warning(f"Job {job_id} cannot be cancelled, status: {job_status['status']}")
                return False
            
            # Try to cancel the future if it exists
            future = job_status.get('data', {}).get('future')
            if future:
                cancelled = future.cancel()
                if cancelled:
                    self.job_tracker.update_job_status(job_id, 'cancelled', {
                        'cancelled_at': datetime.utcnow()
                    })
                    logger.info(f"Job {job_id} cancelled successfully")
                    return True
                else:
                    logger.warning(f"Could not cancel running job: {job_id}")
                    return False
            else:
                # Mark as cancelled even if no future reference
                self.job_tracker.update_job_status(job_id, 'cancelled', {
                    'cancelled_at': datetime.utcnow()
                })
                logger.info(f"Job {job_id} marked as cancelled")
                return True
                
        except Exception as e:
            logger.error(f"Error cancelling job {job_id}: {e}")
            return False
    
    def generate_reports_for_tenants(self, tenant_ids: List[str]) -> Dict[str, str]:
        """Generate reports for multiple tenants asynchronously.
        
        Args:
            tenant_ids: List of tenant identifiers
            
        Returns:
            Dictionary mapping tenant_id to job_id
        """
        logger.info(f"BULK GENERATION: Starting report generation for {len(tenant_ids)} tenants: {tenant_ids}")
        job_mapping = {}
        
        for tenant_id in tenant_ids:
            try:
                logger.info(f"PROCESSING TENANT: {tenant_id}")
                job_id = self.generate_report_async(tenant_id)
                job_mapping[tenant_id] = job_id
                logger.info(f"TENANT PROCESSED: Started report generation for tenant {tenant_id}: {job_id}")
                
            except Exception as e:
                logger.error(f"TENANT FAILED: Failed to start report generation for tenant {tenant_id}: {e}")
                job_mapping[tenant_id] = None
        
        logger.info(f"🏁 BULK GENERATION COMPLETE: Job mapping: {job_mapping}")
        return job_mapping
    
    def cleanup_old_jobs(self, days_old: int = 30) -> int:
        """Clean up old job records.
        
        Args:
            days_old: Remove jobs older than this many days
            
        Returns:
            Number of jobs cleaned up
        """
        return self.job_tracker.cleanup_old_jobs(days_old)
    
    def get_service_health(self) -> Dict:
        """Get health status of all services.
        
        Returns:
            Dictionary with service health information
        """
        health = {
            'excel_service': {'status': 'unknown'},
            'storage_service': {'status': 'unknown'},
            'notification_service': {'status': 'unknown'},
            'job_tracker': {'status': 'unknown'}
        }
        
        # Test storage service
        try:
            if self.storage_service.test_connection():
                health['storage_service'] = {'status': 'healthy'}
            else:
                health['storage_service'] = {'status': 'unhealthy'}
        except Exception as e:
            health['storage_service'] = {'status': 'unhealthy', 'error': str(e)}
        
        # Test notification service
        try:
            if self.notification_service.test_connection():
                health['notification_service'] = {'status': 'healthy'}
            else:
                health['notification_service'] = {'status': 'unhealthy'}
        except Exception as e:
            health['notification_service'] = {'status': 'unhealthy', 'error': str(e)}
        
        # Test Excel service (via database connections)
        try:
            if (self.excel_service.db_connector.test_connection() and 
                self.excel_service.bq_connector.test_connection()):
                health['excel_service'] = {'status': 'healthy'}
            else:
                health['excel_service'] = {'status': 'unhealthy'}
        except Exception as e:
            health['excel_service'] = {'status': 'unhealthy', 'error': str(e)}
        
        # Job tracker is always healthy (in-memory)
        health['job_tracker'] = {'status': 'healthy'}
        
        return health
    
    def shutdown(self):
        """Shutdown the report manager and clean up resources."""
        logger.info("Shutting down report manager")
        
        # Shutdown thread pool
        self.executor.shutdown(wait=True)
        
        # Close service connections
        if hasattr(self.excel_service.db_connector, 'close'):
            self.excel_service.db_connector.close()
        
        if hasattr(self.excel_service.bq_connector, 'close'):
            self.excel_service.bq_connector.close()
        
        logger.info("Report manager shutdown complete")