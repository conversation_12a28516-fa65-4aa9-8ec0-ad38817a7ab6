"""Scheduler manager for APScheduler integration in Marine HMM Reports API."""

import logging
import os
import time
import fcntl
from datetime import datetime
from typing import Dict, List, Optional
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
from flask import current_app

from config.scheduler_config import SchedulerConfig
from managers.report_manager import ReportManager

logger = logging.getLogger(__name__)


class SchedulerManager:
    """Manager for APScheduler integration and job scheduling."""
    
    def __init__(self, report_manager: ReportManager = None):
        """Initialize scheduler manager.
        
        Args:
            report_manager: Report manager for executing scheduled jobs
        """
        self.report_manager = report_manager or ReportManager()
        self.scheduler = None
        self.config = None
        self._scheduler_running = False
        self._initialize_scheduler()
    
    def _initialize_scheduler(self):
        """Initialize APScheduler with configuration."""
        try:
            # Get scheduler configuration
            self.config = SchedulerConfig.get_scheduler_config()
            
            # Create scheduler
            self.scheduler = BackgroundScheduler(
                timezone=self.config['timezone'],
                daemon=True
            )
            
            # Add event listeners
            self.scheduler.add_listener(
                self._job_executed_listener, 
                EVENT_JOB_EXECUTED
            )
            self.scheduler.add_listener(
                self._job_error_listener, 
                EVENT_JOB_ERROR
            )
            
            logger.info("APScheduler initialized successfully")
            SchedulerConfig.log_scheduler_config(self.config)
            
        except Exception as e:
            logger.error(f"Failed to initialize APScheduler: {e}")
            raise
    
    def start_scheduler(self):
        """Start the APScheduler."""
        try:
            if not self.scheduler.running:
                self.scheduler.start()
                logger.info("APScheduler started successfully")
                
                # Schedule monthly reports if enabled
                if self.config['enabled']:
                    self._schedule_monthly_reports()
                else:
                    logger.info("Scheduler is disabled, no jobs scheduled")
            else:
                logger.warning("APScheduler is already running")
                
        except Exception as e:
            logger.error(f"Failed to start APScheduler: {e}")
            raise
    
    def stop_scheduler(self, wait: bool = True):
        """Stop the APScheduler.
        
        Args:
            wait: Whether to wait for running jobs to complete
        """
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=wait)
                logger.info("APScheduler stopped successfully")
            else:
                logger.warning("APScheduler is not running")
                
        except Exception as e:
            logger.error(f"Failed to stop APScheduler: {e}")
            raise
    
    def _schedule_monthly_reports(self):
        """Schedule monthly report generation jobs."""
        try:
            # Get enabled tenants from Flask config
            enabled_tenants = self._get_enabled_tenants()
            
            if not enabled_tenants:
                logger.warning("No enabled tenants found, no monthly reports scheduled")
                return
            
            # Create cron trigger for monthly execution
            trigger = CronTrigger(
                day=self.config['day'],
                hour=self.config['hour'],
                minute=self.config['minute'],
                timezone=self.config['timezone']
            )
            
            # Schedule job for all tenants with strict locking
            job_id = 'monthly_reports_all_tenants'
            
            self.scheduler.add_job(
                func=self._execute_monthly_reports,
                trigger=trigger,
                id=job_id,
                name='Monthly HMM Reports Generation',
                args=[enabled_tenants],
                replace_existing=True,
                max_instances=1,  # Only allow 1 instance at a time
                coalesce=True,    # Combine multiple pending executions into one
                misfire_grace_time=300  # 5 minutes grace time for missed executions
            )
            
            logger.info(f"Scheduled monthly reports for {len(enabled_tenants)} tenants")
            logger.info(f"Next run: {self.scheduler.get_job(job_id).next_run_time}")
            
        except Exception as e:
            logger.error(f"Failed to schedule monthly reports: {e}")
            raise
    
    def _get_enabled_tenants(self) -> List[str]:
        """Get list of enabled tenant IDs from Flask configuration.
        
        Returns:
            List of enabled tenant IDs
        """
        try:
            return current_app.config['TENANT_CONFIG']['enabled_tenant_ids']
        except (RuntimeError, KeyError):
            # Fallback to environment variable if Flask context not available
            import os
            tenant_ids_str = os.getenv('ENABLED_FOR_TENANT_IDS', '')
            if tenant_ids_str:
                return [tenant.strip() for tenant in tenant_ids_str.split(',') if tenant.strip()]
            return []
    
    def _execute_monthly_reports(self, tenant_ids: List[str]):
        """Execute monthly report generation for all tenants.
        
        Args:
            tenant_ids: List of tenant IDs to generate reports for
        """
        lock_file_path = '/tmp/marine_hmm_scheduler.lock'
        
        try:
            # Try to acquire file lock
            lock_file = open(lock_file_path, 'w')
            fcntl.flock(lock_file.fileno(), fcntl.LOCK_EX | fcntl.LOCK_NB)
            
            # Write process info to lock file
            lock_file.write(f"PID: {os.getpid()}\nStarted: {datetime.utcnow().isoformat()}\n")
            lock_file.flush()
            
            logger.info(f"SCHEDULER LOCK ACQUIRED: PID {os.getpid()}")
            logger.info(f"SCHEDULER TRIGGERED: Starting scheduled monthly report generation for {len(tenant_ids)} tenants: {tenant_ids}")
            
            # Generate reports for all tenants
            job_mapping = self.report_manager.generate_reports_for_tenants(tenant_ids)
            
            successful_jobs = [job_id for job_id in job_mapping.values() if job_id is not None]
            failed_tenants = [tenant_id for tenant_id, job_id in job_mapping.items() if job_id is None]
            
            logger.info(f"Scheduled report generation: {len(successful_jobs)} successful, "
                       f"{len(failed_tenants)} failed")
            logger.info(f"Job mapping: {job_mapping}")
            
            if failed_tenants:
                logger.warning(f"Failed to schedule reports for tenants: {failed_tenants}")
            
        except BlockingIOError:
            logger.warning("🔒 Scheduler lock already held by another process, skipping execution")
            return
        except Exception as e:
            logger.error(f"Error in scheduled monthly report execution: {e}")
            raise
        finally:
            try:
                fcntl.flock(lock_file.fileno(), fcntl.LOCK_UN)
                lock_file.close()
                os.remove(lock_file_path)
            except:
                pass  # Ignore cleanup errors
    
    def _job_executed_listener(self, event):
        """Handle job execution events.
        
        Args:
            event: Job execution event
        """
        logger.info(f"Scheduled job executed successfully: {event.job_id}")
    
    def _job_error_listener(self, event):
        """Handle job error events.
        
        Args:
            event: Job error event
        """
        logger.error(f"Scheduled job failed: {event.job_id}, exception: {event.exception}")
        
        # Send error notification
        try:
            self.report_manager.notification_service.publish_error_notification(
                'scheduled_job_failed',
                str(event.exception),
                {'job_id': event.job_id, 'scheduled_run_time': str(event.scheduled_run_time)}
            )
        except Exception as notify_error:
            logger.error(f"Failed to send job error notification: {notify_error}")
    
    def add_custom_job(self, func, trigger, job_id: str, name: str = None, 
                      args: tuple = None, kwargs: dict = None, **job_kwargs) -> bool:
        """Add a custom scheduled job.
        
        Args:
            func: Function to execute
            trigger: APScheduler trigger
            job_id: Unique job identifier
            name: Human-readable job name
            args: Function arguments
            kwargs: Function keyword arguments
            **job_kwargs: Additional job configuration
            
        Returns:
            True if job added successfully, False otherwise
        """
        try:
            self.scheduler.add_job(
                func=func,
                trigger=trigger,
                id=job_id,
                name=name or job_id,
                args=args,
                kwargs=kwargs,
                replace_existing=True,
                **job_kwargs
            )
            
            logger.info(f"Custom job added: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add custom job {job_id}: {e}")
            return False
    
    def remove_job(self, job_id: str) -> bool:
        """Remove a scheduled job.
        
        Args:
            job_id: Job identifier to remove
            
        Returns:
            True if job removed successfully, False otherwise
        """
        try:
            self.scheduler.remove_job(job_id)
            logger.info(f"Job removed: {job_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove job {job_id}: {e}")
            return False
    
    def get_jobs(self) -> List[Dict]:
        """Get list of all scheduled jobs.
        
        Returns:
            List of job information dictionaries
        """
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'func': str(job.func),
                    'trigger': str(job.trigger),
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'args': list(job.args) if job.args else [],
                    'kwargs': dict(job.kwargs) if job.kwargs else {}
                })
            
            return jobs
            
        except Exception as e:
            logger.error(f"Failed to get jobs list: {e}")
            return []
    
    def get_job_info(self, job_id: str) -> Optional[Dict]:
        """Get information about a specific job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job information dictionary or None if not found
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                return {
                    'id': job.id,
                    'name': job.name,
                    'func': str(job.func),
                    'trigger': str(job.trigger),
                    'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                    'args': list(job.args) if job.args else [],
                    'kwargs': dict(job.kwargs) if job.kwargs else {}
                }
            return None
            
        except Exception as e:
            logger.error(f"Failed to get job info for {job_id}: {e}")
            return None
    
    def update_schedule_config(self, new_config: Dict) -> bool:
        """Update scheduler configuration and reschedule jobs.
        
        Args:
            new_config: New scheduler configuration
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            # Validate new configuration
            SchedulerConfig.validate_config(new_config)
            
            # Update configuration
            self.config.update(new_config)
            
            # Remove existing monthly reports job
            try:
                self.scheduler.remove_job('monthly_reports_all_tenants')
            except:
                pass  # Job might not exist
            
            # Reschedule if enabled
            if self.config['enabled']:
                self._schedule_monthly_reports()
            
            logger.info("Scheduler configuration updated successfully")
            SchedulerConfig.log_scheduler_config(self.config)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update scheduler configuration: {e}")
            return False
    
    def trigger_job_now(self, job_id: str) -> bool:
        """Trigger a scheduled job to run immediately.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if job triggered successfully, False otherwise
        """
        try:
            job = self.scheduler.get_job(job_id)
            if job:
                job.modify(next_run_time=datetime.now())
                logger.info(f"Job {job_id} triggered to run immediately")
                return True
            else:
                logger.warning(f"Job not found: {job_id}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to trigger job {job_id}: {e}")
            return False
    
    def is_running(self) -> bool:
        """Check if scheduler is running.
        
        Returns:
            True if scheduler is running, False otherwise
        """
        return self.scheduler is not None and self.scheduler.running
    
    def get_scheduler_info(self) -> Dict:
        """Get scheduler status and configuration information.
        
        Returns:
            Dictionary with scheduler information
        """
        return {
            'running': self.is_running(),
            'config': self.config,
            'job_count': len(self.scheduler.get_jobs()) if self.scheduler else 0,
            'timezone': self.config['timezone'] if self.config else None,
            'next_monthly_run': self.get_job_info('monthly_reports_all_tenants')
        }