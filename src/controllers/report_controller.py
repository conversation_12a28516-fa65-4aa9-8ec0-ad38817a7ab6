"""Report controller for Marine HMM Reports API."""

from flask import Blueprint, jsonify, request, current_app
import logging
from managers.report_manager import ReportManager
from utils.validators import validate_tenant_id, validate_job_id

logger = logging.getLogger(__name__)

# Create blueprint
report_bp = Blueprint('reports', __name__)

# Initialize report manager (will be injected in production)
report_manager = ReportManager()


@report_bp.route('/generate', methods=['POST'])
def generate_reports():
    """Trigger report generation endpoint.
    
    Expected JSON payload:
    {
        "tenant_id": "string",
        "async": true/false (optional, default: true)
    }
    
    Returns:
        JSON response with job information
    """
    try:
        # Get request data
        data = request.get_json() or {}
        
        # Get tenant_id from request or use default from environment
        tenant_id = data.get('tenant_id')
        
        # If no tenant_id provided, use the first enabled tenant from environment
        if not tenant_id:
            enabled_tenants = current_app.config.get('TENANT_CONFIG', {}).get('enabled_tenant_ids', [])
            if enabled_tenants:
                tenant_id = enabled_tenants[0]  # Use first enabled tenant as default
                logger.info(f"No tenant_id provided, using default: {tenant_id}")
            else:
                return jsonify({
                    "error": {
                        "code": "MISSING_TENANT_ID",
                        "message": "tenant_id is required and no default tenant configured",
                        "status_code": 400
                    },
                    "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
                }), 400
        
        # Validate tenant ID format
        if not validate_tenant_id(tenant_id):
            return jsonify({
                "error": {
                    "code": "INVALID_TENANT_ID",
                    "message": "Invalid tenant_id format",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        # Check if tenant is enabled
        enabled_tenants = current_app.config.get('TENANT_CONFIG', {}).get('enabled_tenant_ids', [])
        if tenant_id not in enabled_tenants:
            return jsonify({
                "error": {
                    "code": "TENANT_NOT_ENABLED",
                    "message": f"Tenant {tenant_id} is not enabled for report generation",
                    "status_code": 403
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 403
        
        # Get async flag (default to True)
        async_mode = data.get('async', True)
        user_id = data.get('user_id')  # Optional user identifier
        
        logger.info(f"Report generation requested for tenant: {tenant_id}, async: {async_mode}")
        
        if async_mode:
            # Generate report asynchronously
            job_id = report_manager.generate_report_async(tenant_id, user_id)
            
            response = {
                "job_id": job_id,
                "status": "pending",
                "message": "Report generation started",
                "tenant_id": tenant_id,
                "async": True,
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }
            
            return jsonify(response), 202
        
        else:
            # Generate report synchronously
            result = report_manager.generate_report_sync(tenant_id)
            
            response = {
                "status": result['status'],
                "message": "Report generated successfully",
                "tenant_id": tenant_id,
                "gcs_path": result['gcs_path'],
                "filename": result['filename'],
                "async": False,
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }
            
            return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Report generation request failed: {str(e)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "Failed to process report generation request",
                "status_code": 500
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 500


@report_bp.route('/generate-all', methods=['POST'])
def generate_all_reports():
    """Generate reports for all enabled tenants.
    
    Returns:
        JSON response with job information for all tenants
    """
    try:
        logger.info("Bulk report generation requested for all enabled tenants")
        
        # Get all enabled tenants
        enabled_tenants = current_app.config.get('TENANT_CONFIG', {}).get('enabled_tenant_ids', [])
        
        if not enabled_tenants:
            return jsonify({
                "error": {
                    "code": "NO_TENANTS_ENABLED",
                    "message": "No tenants are enabled for report generation",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        # Generate reports for all tenants
        job_mapping = report_manager.generate_reports_for_tenants(enabled_tenants)
        
        # Prepare response
        jobs = []
        successful_jobs = 0
        failed_tenants = []
        
        for tenant_id, job_id in job_mapping.items():
            if job_id:
                jobs.append({
                    "tenant_id": tenant_id,
                    "job_id": job_id,
                    "status": "pending"
                })
                successful_jobs += 1
            else:
                failed_tenants.append(tenant_id)
        
        response = {
            "message": f"Bulk report generation started for {successful_jobs} tenants",
            "total_tenants": len(enabled_tenants),
            "successful_jobs": successful_jobs,
            "failed_tenants": failed_tenants,
            "jobs": jobs,
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 202
        
    except Exception as e:
        logger.error(f"Bulk report generation request failed: {str(e)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "Failed to process bulk report generation request",
                "status_code": 500
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 500


@report_bp.route('/status/<job_id>', methods=['GET'])
def get_job_status(job_id):
    """Get job status endpoint.
    
    Args:
        job_id: Job identifier
        
    Returns:
        JSON response with job status
    """
    try:
        # Validate job ID format
        if not validate_job_id(job_id):
            return jsonify({
                "error": {
                    "code": "INVALID_JOB_ID",
                    "message": "Invalid job_id format",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        logger.info(f"Job status requested for job_id: {job_id}")
        
        # Get job status from report manager
        job_status = report_manager.get_job_status(job_id)
        
        if not job_status:
            return jsonify({
                "error": {
                    "code": "JOB_NOT_FOUND",
                    "message": f"Job {job_id} not found",
                    "status_code": 404
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 404
        
        # Prepare response based on job status
        response = {
            "job_id": job_status['id'],
            "status": job_status['status'],
            "created_at": job_status['created_at'],
            "updated_at": job_status['updated_at'],
            "tenant_id": job_status['data'].get('tenant_id'),
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        # Add additional fields based on status
        if job_status['status'] == 'completed':
            response.update({
                "gcs_path": job_status['data'].get('gcs_path'),
                "filename": job_status['data'].get('filename'),
                "completed_at": job_status['data'].get('completed_at')
            })
        elif job_status['status'] == 'failed':
            response.update({
                "error": job_status['data'].get('error'),
                "failed_at": job_status['data'].get('failed_at')
            })
        elif job_status['status'] == 'running':
            response.update({
                "stage": job_status['data'].get('stage'),
                "started_at": job_status['data'].get('started_at')
            })
        
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Job status request failed: {str(e)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "Failed to get job status",
                "status_code": 500
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 500


@report_bp.route('/jobs', methods=['GET'])
def list_jobs():
    """List report generation jobs with optional filtering.
    
    Query parameters:
    - tenant_id: Filter by tenant ID
    - status: Filter by job status
    - limit: Maximum number of jobs to return (default: 50)
    
    Returns:
        JSON response with list of jobs
    """
    try:
        # Get query parameters
        tenant_id = request.args.get('tenant_id')
        status = request.args.get('status')
        limit = int(request.args.get('limit', 50))
        
        # Validate limit
        if limit < 1 or limit > 1000:
            return jsonify({
                "error": {
                    "code": "INVALID_LIMIT",
                    "message": "Limit must be between 1 and 1000",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        # Validate tenant_id if provided
        if tenant_id and not validate_tenant_id(tenant_id):
            return jsonify({
                "error": {
                    "code": "INVALID_TENANT_ID",
                    "message": "Invalid tenant_id format",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        logger.info(f"Jobs list requested - tenant_id: {tenant_id}, status: {status}, limit: {limit}")
        
        # Get jobs from report manager
        jobs = report_manager.list_jobs(tenant_id=tenant_id, status=status, limit=limit)
        
        # Format response
        response = {
            "jobs": jobs,
            "count": len(jobs),
            "filters": {
                "tenant_id": tenant_id,
                "status": status,
                "limit": limit
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 200
        
    except ValueError as e:
        return jsonify({
            "error": {
                "code": "INVALID_PARAMETER",
                "message": str(e),
                "status_code": 400
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }), 400
        
    except Exception as e:
        logger.error(f"Jobs list request failed: {str(e)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "Failed to list jobs",
                "status_code": 500
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 500


@report_bp.route('/jobs/<job_id>/cancel', methods=['POST'])
def cancel_job(job_id):
    """Cancel a running report generation job.
    
    Args:
        job_id: Job identifier
        
    Returns:
        JSON response with cancellation result
    """
    try:
        # Validate job ID format
        if not validate_job_id(job_id):
            return jsonify({
                "error": {
                    "code": "INVALID_JOB_ID",
                    "message": "Invalid job_id format",
                    "status_code": 400
                },
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }), 400
        
        logger.info(f"Job cancellation requested for job_id: {job_id}")
        
        # Attempt to cancel the job
        cancelled = report_manager.cancel_job(job_id)
        
        if cancelled:
            response = {
                "job_id": job_id,
                "status": "cancelled",
                "message": "Job cancelled successfully",
                "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
            }
            return jsonify(response), 200
        else:
            # Check if job exists
            job_status = report_manager.get_job_status(job_id)
            if not job_status:
                return jsonify({
                    "error": {
                        "code": "JOB_NOT_FOUND",
                        "message": f"Job {job_id} not found",
                        "status_code": 404
                    },
                    "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
                }), 404
            else:
                return jsonify({
                    "error": {
                        "code": "CANNOT_CANCEL_JOB",
                        "message": f"Job {job_id} cannot be cancelled (status: {job_status['status']})",
                        "status_code": 409
                    },
                    "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
                }), 409
        
    except Exception as e:
        logger.error(f"Job cancellation request failed: {str(e)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "Failed to cancel job",
                "status_code": 500
            },
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 500