"""Health check controller for Marine HMM Reports API."""

from flask import Blueprint, jsonify, current_app
import logging
from managers.report_manager import ReportManager

logger = logging.getLogger(__name__)

# Create blueprint
health_bp = Blueprint('health', __name__)

# Initialize report manager for health checks
report_manager = ReportManager()


@health_bp.route('/', methods=['GET'])
def health_check():
    """Basic health check endpoint.
    
    Returns:
        JSON response with service health status
    """
    try:
        response = {
            "status": "healthy",
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "version": "1.0.0"
        }
        
        logger.debug("Health check requested")
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}", exc_info=True)
        
        response = {
            "status": "unhealthy",
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "error": str(e)
        }
        
        return jsonify(response), 503


@health_bp.route('/detailed', methods=['GET'])
def detailed_health_check():
    """Detailed health check endpoint with service dependencies.
    
    Returns:
        JSON response with detailed health status
    """
    try:
        # Get service health from report manager
        service_health = report_manager.get_service_health()
        
        # Determine overall status
        overall_status = "healthy"
        unhealthy_services = []
        
        for service_name, health_info in service_health.items():
            if health_info['status'] != 'healthy':
                overall_status = "degraded"
                unhealthy_services.append(service_name)
        
        if len(unhealthy_services) >= len(service_health) / 2:
            overall_status = "unhealthy"
        
        response = {
            "status": overall_status,
            "service": "marine-hmm-reports-api",
            "version": "1.0.0",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "services": service_health,
            "unhealthy_services": unhealthy_services
        }
        
        # Return appropriate HTTP status
        if overall_status == "healthy":
            return jsonify(response), 200
        elif overall_status == "degraded":
            return jsonify(response), 200  # Still operational
        else:
            return jsonify(response), 503  # Service unavailable
        
    except Exception as e:
        logger.error(f"Detailed health check failed: {str(e)}", exc_info=True)
        
        response = {
            "status": "unhealthy",
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "error": str(e)
        }
        
        return jsonify(response), 503


@health_bp.route('/ready', methods=['GET'])
def readiness_check():
    """Readiness check endpoint for Kubernetes.
    
    Returns:
        JSON response indicating if service is ready to accept requests
    """
    try:
        # Check if critical services are available
        service_health = report_manager.get_service_health()
        
        # Critical services that must be healthy for readiness
        critical_services = ['storage_service', 'excel_service']
        
        ready = True
        failed_services = []
        
        for service in critical_services:
            if service_health.get(service, {}).get('status') != 'healthy':
                ready = False
                failed_services.append(service)
        
        response = {
            "ready": ready,
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "critical_services": {
                service: service_health.get(service, {'status': 'unknown'})
                for service in critical_services
            }
        }
        
        if not ready:
            response["failed_services"] = failed_services
        
        return jsonify(response), 200 if ready else 503
        
    except Exception as e:
        logger.error(f"Readiness check failed: {str(e)}", exc_info=True)
        
        response = {
            "ready": False,
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "error": str(e)
        }
        
        return jsonify(response), 503


@health_bp.route('/live', methods=['GET'])
def liveness_check():
    """Liveness check endpoint for Kubernetes.
    
    Returns:
        JSON response indicating if service is alive
    """
    try:
        # Simple liveness check - just verify the service is responding
        response = {
            "alive": True,
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)()
        }
        
        return jsonify(response), 200
        
    except Exception as e:
        logger.error(f"Liveness check failed: {str(e)}", exc_info=True)
        
        response = {
            "alive": False,
            "service": "marine-hmm-reports-api",
            "timestamp": current_app.config.get('CURRENT_TIME_FUNC', lambda: None)(),
            "error": str(e)
        }
        
        return jsonify(response), 503