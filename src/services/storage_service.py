"""Google Cloud Storage service for Marine HMM Reports API."""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from google.cloud import storage
from google.cloud.exceptions import NotFound
from google.auth import default
from google.auth.transport import requests as auth_requests
from flask import current_app

from src.config.settings import get_tenant_name

logger = logging.getLogger(__name__)


class StorageService:
    """Service for handling Google Cloud Storage operations."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize storage service.
        
        Args:
            config: Optional config dict, uses Flask config if not provided
        """
        self.config = config or self._get_flask_config()
        self.client = None
        self.bucket = None
        # Initialize client during construction for reliable operation
        self._initialize_client()
    
    def _get_flask_config(self) -> Dict:
        """Get GCS configuration from Flask app config.
        
        Returns:
            GCS configuration dictionary
        """
        import os
        
        # Always use environment variables directly to ensure consistency
        config = {
            'project_id': os.getenv('PROJECT_ID', 'prj-nonprod-eng-svc-01'),
            'bucket_name': os.getenv('HMM_REPORTS_BUCKET_NAME', 'hull-nonprod-static-content'),
            'pubsub_topic': os.getenv('PUBSUB_TOPIC', 'marine-reports'),
            'tenant_users_config': os.getenv('TENANT_USERS_CONFIG', '')
        }
        
        # Try to get from Flask config if available, but use env vars as fallback
        try:
            flask_config = current_app.config.get('GCP_CONFIG', {})
            tenant_config = current_app.config.get('TENANT_CONFIG', {})

            # Only override if Flask config has the values
            for key, value in flask_config.items():
                if value is not None:
                    config[key] = value

            if tenant_config.get('tenant_users_config'):
                config['tenant_users_config'] = tenant_config['tenant_users_config']

        except (RuntimeError, KeyError):
            # Flask context not available, use environment variables
            pass
        
        return config
    
    def _initialize_client(self):
        """Initialize GCS client and bucket."""
        try:
            self.client = storage.Client(project=self.config['project_id'])
            self.bucket = self.client.bucket(self.config['bucket_name'])
            
            logger.info(f"GCS client initialized for project: {self.config['project_id']}, "
                       f"bucket: {self.config['bucket_name']}")
            
        except Exception as e:
            logger.error(f"Failed to initialize GCS client: {e}")
            raise
    
    def _ensure_client(self):
        """Ensure GCS client is initialized."""
        if self.client is None or self.bucket is None:
            self._initialize_client()
    
    def upload_file(self, local_file_path: str, destination_blob_name: str, 
                   content_type: str = None) -> str:
        """Upload a file to Google Cloud Storage.
        
        Args:
            local_file_path: Path to the local file
            destination_blob_name: Name for the blob in GCS
            content_type: MIME type of the file
            
        Returns:
            GCS path of the uploaded file
        """
        try:
            self._ensure_client()
            
            blob = self.bucket.blob(destination_blob_name)
            
            # Set content type if provided
            if content_type:
                blob.content_type = content_type
            elif destination_blob_name.endswith('.xlsx'):
                blob.content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            # Upload file
            blob.upload_from_filename(local_file_path)
            
            gcs_path = f"gs://{self.config['bucket_name']}/{destination_blob_name}"
            
            logger.info(f"File uploaded successfully: {gcs_path}")
            return gcs_path
            
        except Exception as e:
            logger.error(f"Error uploading file to GCS: {e}")
            raise
    
    def upload_from_stream(self, stream, destination_blob_name: str, 
                          content_type: str = None) -> str:
        """Upload data from a stream/buffer to Google Cloud Storage.
        
        Args:
            stream: File-like object or BytesIO buffer
            destination_blob_name: Name for the blob in GCS
            content_type: MIME type of the data
            
        Returns:
            GCS path of the uploaded data
        """
        try:
            self._ensure_client()
            
            blob = self.bucket.blob(destination_blob_name)
            
            # Set content type if provided
            if content_type:
                blob.content_type = content_type
            elif destination_blob_name.endswith('.xlsx'):
                blob.content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            
            # Reset stream position to beginning
            stream.seek(0)
            
            # Upload from stream
            blob.upload_from_file(stream)
            
            gcs_path = f"gs://{self.config['bucket_name']}/{destination_blob_name}"
            
            logger.info(f"Stream data uploaded successfully: {gcs_path}")
            return gcs_path
            
        except Exception as e:
            logger.error(f"Error uploading stream data to GCS: {e}")
            raise

    def upload_from_string(self, data: str, destination_blob_name: str, 
                          content_type: str = 'text/plain') -> str:
        """Upload string data to Google Cloud Storage.
        
        Args:
            data: String data to upload
            destination_blob_name: Name for the blob in GCS
            content_type: MIME type of the data
            
        Returns:
            GCS path of the uploaded data
        """
        try:
            blob = self.bucket.blob(destination_blob_name)
            blob.content_type = content_type
            
            blob.upload_from_string(data)
            
            gcs_path = f"gs://{self.config['bucket_name']}/{destination_blob_name}"
            
            logger.info(f"String data uploaded successfully: {gcs_path}")
            return gcs_path
            
        except Exception as e:
            logger.error(f"Error uploading string data to GCS: {e}")
            raise
    
    def download_file(self, blob_name: str, local_file_path: str) -> bool:
        """Download a file from Google Cloud Storage.
        
        Args:
            blob_name: Name of the blob in GCS
            local_file_path: Local path to save the file
            
        Returns:
            True if download successful, False otherwise
        """
        try:
            blob = self.bucket.blob(blob_name)
            blob.download_to_filename(local_file_path)
            
            logger.info(f"File downloaded successfully: {local_file_path}")
            return True
            
        except NotFound:
            logger.warning(f"Blob not found: {blob_name}")
            return False
        except Exception as e:
            logger.error(f"Error downloading file from GCS: {e}")
            return False
    
    def delete_file(self, blob_name: str) -> bool:
        """Delete a file from Google Cloud Storage.
        
        Args:
            blob_name: Name of the blob to delete
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            blob = self.bucket.blob(blob_name)
            blob.delete()
            
            logger.info(f"File deleted successfully: {blob_name}")
            return True
            
        except NotFound:
            logger.warning(f"Blob not found for deletion: {blob_name}")
            return False
        except Exception as e:
            logger.error(f"Error deleting file from GCS: {e}")
            return False
    
    def file_exists(self, blob_name: str) -> bool:
        """Check if a file exists in Google Cloud Storage.
        
        Args:
            blob_name: Name of the blob to check
            
        Returns:
            True if file exists, False otherwise
        """
        try:
            blob = self.bucket.blob(blob_name)
            return blob.exists()
            
        except Exception as e:
            logger.error(f"Error checking file existence in GCS: {e}")
            return False
    
    def get_file_info(self, blob_name: str) -> Optional[Dict]:
        """Get information about a file in Google Cloud Storage.
        
        Args:
            blob_name: Name of the blob
            
        Returns:
            Dictionary with file information or None if not found
        """
        try:
            blob = self.bucket.blob(blob_name)
            blob.reload()
            
            return {
                'name': blob.name,
                'size': blob.size,
                'content_type': blob.content_type,
                'created': blob.time_created,
                'updated': blob.updated,
                'md5_hash': blob.md5_hash,
                'public_url': blob.public_url,
                'media_link': blob.media_link
            }
            
        except NotFound:
            logger.warning(f"Blob not found: {blob_name}")
            return None
        except Exception as e:
            logger.error(f"Error getting file info from GCS: {e}")
            return None
    
    def generate_signed_url(self, blob_name: str, expiration_hours: int = 24, 
                           method: str = 'GET') -> Optional[str]:
        """Generate a signed URL for accessing a file.
        
        Args:
            blob_name: Name of the blob
            expiration_hours: Hours until the URL expires
            method: HTTP method (GET, PUT, POST, DELETE)
            
        Returns:
            Signed URL or None if generation failed
        """
        try:
            blob = self.bucket.blob(blob_name)
            
            expiration = datetime.utcnow() + timedelta(hours=expiration_hours)
            
            # Get current credentials and service account info for Workload Identity
            credentials, _ = default()
            
            # For Workload Identity (GKE), use IAM signing
            if hasattr(credentials, 'service_account_email') and credentials.service_account_email:
                # Refresh token if needed
                auth_request = auth_requests.Request()
                credentials.refresh(auth_request)
                
                signed_url = blob.generate_signed_url(
                    expiration=expiration,
                    method=method,
                    version='v4',
                    service_account_email=credentials.service_account_email,
                    access_token=credentials.token
                )
            else:
                # Fallback to standard signing (for service account key files)
                signed_url = blob.generate_signed_url(
                    expiration=expiration,
                    method=method,
                    version='v4'
                )
            
            logger.info(f"Signed URL generated for {blob_name}, expires in {expiration_hours} hours")
            return signed_url
            
        except Exception as e:
            logger.error(f"Error generating signed URL: {e}")
            return None
    
    def list_files(self, prefix: str = None, max_results: int = 100) -> List[Dict]:
        """List files in the bucket.
        
        Args:
            prefix: Filter files by prefix
            max_results: Maximum number of results to return
            
        Returns:
            List of file information dictionaries
        """
        try:
            blobs = self.client.list_blobs(
                self.bucket,
                prefix=prefix,
                max_results=max_results
            )
            
            files = []
            for blob in blobs:
                files.append({
                    'name': blob.name,
                    'size': blob.size,
                    'content_type': blob.content_type,
                    'created': blob.time_created,
                    'updated': blob.updated
                })
            
            logger.info(f"Listed {len(files)} files with prefix: {prefix}")
            return files
            
        except Exception as e:
            logger.error(f"Error listing files in GCS: {e}")
            return []
    
    def generate_report_filename(self, tenant_id: str = None, timestamp: datetime = None) -> str:
        """Generate a standardized filename for reports.
        
        Args:
            tenant_id: Optional tenant identifier
            timestamp: Optional timestamp, uses current time if not provided
            
        Returns:
            Generated filename
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        timestamp_str = timestamp.strftime("%Y%m%d")
        
        if tenant_id:
            # Get tenant name from configuration, fallback to tenant_id if not found
            tenant_name = get_tenant_name(tenant_id, self.config.get('tenant_users_config'))

            # Sanitize tenant name for filename (replace spaces and special chars with underscores)
            safe_tenant_name = "".join(c if c.isalnum() else "_" for c in tenant_name)

            return f"Portfolio_HMM_Report_{safe_tenant_name}_{timestamp_str}.xlsx"
        else:
            return f"Portfolio_HMM_Report_{timestamp_str}.xlsx"
    
    def test_connection(self) -> bool:
        """Test GCS connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Try to get bucket metadata
            self.bucket.reload()
            logger.info("GCS connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"GCS connection test failed: {e}")
            return False
    
    def get_connection_info(self) -> Dict:
        """Get current connection configuration info.
        
        Returns:
            Dictionary with connection details
        """
        return {
            'project_id': self.config['project_id'],
            'bucket_name': self.config['bucket_name'],
            'client_active': self.client is not None,
            'bucket_active': self.bucket is not None
        }