"""Flask application factory for Marine HMM Reports API."""

import os
import logging
import atexit
from flask import Flask, jsonify
from flask_cors import CORS
from werkzeug.exceptions import HTTPException

# Global scheduler manager instance
scheduler_manager = None


def create_app(config_name=None):
    """Create and configure Flask application.
    
    Args:
        config_name: Configuration environment name (dev, staging, prod)
        
    Returns:
        Flask application instance
    """
    app = Flask(__name__)
    
    # Load configuration
    config_name = config_name or os.getenv('FLASK_ENV', 'production')
    app.config.from_object(f'config.settings.{config_name.title()}Config')
    
    # Configure CORS
    CORS(app, origins=app.config.get('CORS_ORIGINS', ['*']))
    
    # Setup logging
    setup_logging(app)
    
    # Initialize scheduler
    setup_scheduler(app)
    
    # Register blueprints
    register_blueprints(app)
    
    # Setup error handlers
    setup_error_handlers(app)
    
    # Setup cleanup handlers
    setup_cleanup_handlers(app)
    
    # Log application startup
    app.logger.info(f"Flask application created with config: {config_name}")
    
    return app


def register_blueprints(app):
    """Register application blueprints.
    
    Args:
        app: Flask application instance
    """
    # Import blueprints here to avoid circular imports
    from controllers.health_controller import health_bp
    from controllers.report_controller import report_bp
    
    # Register blueprints
    app.register_blueprint(health_bp, url_prefix='/health')
    app.register_blueprint(report_bp, url_prefix='/api/reports')
    
    app.logger.info("Blueprints registered successfully")


def setup_error_handlers(app):
    """Setup global error handlers.
    
    Args:
        app: Flask application instance
    """
    @app.errorhandler(HTTPException)
    def handle_http_exception(error):
        """Handle HTTP exceptions with standardized response format."""
        response = {
            "error": {
                "code": error.name.upper().replace(' ', '_'),
                "message": error.description,
                "status_code": error.code
            },
            "timestamp": app.config.get('CURRENT_TIME_FUNC', lambda: None)() or None,
            "request_id": getattr(app, 'current_request_id', None)
        }
        
        app.logger.error(f"HTTP {error.code} error: {error.description}")
        return jsonify(response), error.code
    
    @app.errorhandler(Exception)
    def handle_generic_exception(error):
        """Handle generic exceptions with standardized response format."""
        app.logger.error(f"Unhandled exception: {str(error)}", exc_info=True)
        
        response = {
            "error": {
                "code": "INTERNAL_SERVER_ERROR",
                "message": "An internal server error occurred",
                "status_code": 500
            },
            "timestamp": app.config.get('CURRENT_TIME_FUNC', lambda: None)() or None,
            "request_id": getattr(app, 'current_request_id', None)
        }
        
        return jsonify(response), 500
    
    app.logger.info("Error handlers configured successfully")


def setup_logging(app):
    """Setup application logging configuration.
    
    Args:
        app: Flask application instance
    """
    # Configure logging level based on environment
    log_level = getattr(logging, app.config.get('LOG_LEVEL', 'INFO').upper())
    
    # Configure root logger
    logging.basicConfig(
        level=log_level,
        format=app.config.get('LOG_FORMAT', 
                             '%(asctime)s - %(name)s - %(levelname)s - %(message)s'),
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    # Set Flask app logger level
    app.logger.setLevel(log_level)
    
    # Suppress werkzeug logs in production
    if app.config.get('FLASK_ENV') == 'production':
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
    
    app.logger.info(f"Logging configured with level: {logging.getLevelName(log_level)}")


def setup_scheduler(app):
    """Setup APScheduler integration.
    
    Args:
        app: Flask application instance
    """
    global scheduler_manager
    
    try:
        from managers.scheduler_manager import SchedulerManager
        
        with app.app_context():
            scheduler_manager = SchedulerManager()
            
            # Start scheduler if enabled
            if app.config.get('SCHEDULER_CONFIG', {}).get('enabled', True):
                scheduler_manager.start_scheduler()
                app.logger.info("APScheduler started successfully")
            else:
                app.logger.info("APScheduler disabled by configuration")
        
    except Exception as e:
        app.logger.error(f"Failed to setup scheduler: {e}")
        # Don't fail app startup if scheduler fails
        scheduler_manager = None


def setup_cleanup_handlers(app):
    """Setup cleanup handlers for graceful shutdown.
    
    Args:
        app: Flask application instance
    """
    def cleanup():
        """Cleanup function called on app shutdown."""
        global scheduler_manager
        
        app.logger.info("Starting application cleanup")
        
        # Stop scheduler
        if scheduler_manager:
            try:
                scheduler_manager.stop_scheduler()
                app.logger.info("Scheduler stopped successfully")
            except Exception as e:
                app.logger.error(f"Error stopping scheduler: {e}")
        
        app.logger.info("Application cleanup completed")
    
    # Register cleanup function
    atexit.register(cleanup)
    
    # Store cleanup function in app for manual cleanup if needed
    app.cleanup = cleanup