"""BigQuery connector for Marine HMM Reports API."""

import pandas as pd
from google.cloud import bigquery
from google.cloud.exceptions import NotFound
import logging
from typing import Dict, Optional, List
from flask import current_app

logger = logging.getLogger(__name__)


class BigQueryConnector:
    """Handles BigQuery connections and queries."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize BigQuery connector.
        
        Args:
            config: Optional config dict, uses Flask config if not provided
        """
        self.config = config or self._get_flask_config()
        self.client = None
        # Create client during initialization for reliable operation
        self._create_client()
    
    def _get_flask_config(self) -> Dict:
        """Get BigQuery configuration from Flask app config.
        
        Returns:
            BigQuery configuration dictionary
        """
        import os
        
        # Always use environment variables directly to ensure consistency
        config = {
            'project_id': os.getenv('PROJECT_ID', 'prj-nonprod-eng-svc-01'),
            'bucket_name': os.getenv('HMM_REPORTS_BUCKET_NAME', 'hull-nonprod-static-content'),
            'pubsub_topic': os.getenv('PUBSUB_TOPIC', 'marine-reports')
        }
        
        # Try to get from Flask config if available, but use env vars as fallback
        try:
            flask_config = current_app.config.get('GCP_CONFIG', {})
            # Only override if Flask config has the values
            for key, value in flask_config.items():
                if value is not None:
                    config[key] = value
        except (RuntimeError, KeyError):
            # Flask context not available, use environment variables
            pass
        
        return config
    
    def _create_client(self):
        """Create BigQuery client."""
        try:
            self.client = bigquery.Client(project=self.config['project_id'])
            logger.info(f"BigQuery client created for project: {self.config['project_id']}")
            
        except Exception as e:
            logger.error(f"Failed to create BigQuery client: {e}")
            raise
    
    def _ensure_client(self):
        """Ensure BigQuery client is initialized."""
        if self.client is None:
            self._create_client()
    
    def execute_query(self, query: str, params: Optional[List] = None) -> pd.DataFrame:
        """Execute a BigQuery SQL query and return results as DataFrame.
        
        Args:
            query: SQL query string
            params: Optional query parameters
            
        Returns:
            DataFrame with query results
        """
        try:
            self._ensure_client()
            
            job_config = bigquery.QueryJobConfig()
            
            if params:
                job_config.query_parameters = params
            
            # Execute query
            query_job = self.client.query(query, job_config=job_config)
            
            # Wait for job to complete and get results
            results = query_job.result()
            
            # Convert to DataFrame
            df = results.to_dataframe()
            
            logger.info(f"BigQuery query executed successfully, returned {len(df)} rows")
            logger.debug(f"Query: {query}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error executing BigQuery query: {e}")
            logger.debug(f"Query: {query}")
            if params:
                logger.debug(f"Params: {params}")
            raise
    
    def execute_query_to_table(self, query: str, destination_table: str, 
                              write_disposition: str = 'WRITE_TRUNCATE') -> str:
        """Execute a query and write results to a BigQuery table.
        
        Args:
            query: SQL query string
            destination_table: Destination table in format 'dataset.table'
            write_disposition: Write disposition (WRITE_TRUNCATE, WRITE_APPEND, WRITE_EMPTY)
            
        Returns:
            Job ID of the query job
        """
        try:
            job_config = bigquery.QueryJobConfig(
                destination=destination_table,
                write_disposition=write_disposition
            )
            
            query_job = self.client.query(query, job_config=job_config)
            query_job.result()  # Wait for job to complete
            
            logger.info(f"Query results written to table: {destination_table}")
            return query_job.job_id
            
        except Exception as e:
            logger.error(f"Error executing query to table: {e}")
            raise
    
    def get_table_info(self, table_id: str) -> Dict:
        """Get information about a BigQuery table.
        
        Args:
            table_id: Table ID in format 'dataset.table'
            
        Returns:
            Dictionary with table information
        """
        try:
            table_ref = self.client.dataset(table_id.split('.')[0]).table(table_id.split('.')[1])
            table = self.client.get_table(table_ref)
            
            return {
                'table_id': table.table_id,
                'dataset_id': table.dataset_id,
                'project': table.project,
                'num_rows': table.num_rows,
                'num_bytes': table.num_bytes,
                'created': table.created,
                'modified': table.modified,
                'schema': [{'name': field.name, 'type': field.field_type} for field in table.schema]
            }
            
        except NotFound:
            logger.warning(f"Table not found: {table_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting table info: {e}")
            raise
    
    def table_exists(self, table_id: str) -> bool:
        """Check if a BigQuery table exists.
        
        Args:
            table_id: Table ID in format 'dataset.table'
            
        Returns:
            True if table exists, False otherwise
        """
        try:
            table_ref = self.client.dataset(table_id.split('.')[0]).table(table_id.split('.')[1])
            self.client.get_table(table_ref)
            return True
        except NotFound:
            return False
        except Exception as e:
            logger.error(f"Error checking table existence: {e}")
            raise
    
    def create_dataset(self, dataset_id: str, location: str = 'US') -> bool:
        """Create a BigQuery dataset if it doesn't exist.
        
        Args:
            dataset_id: Dataset ID
            location: Dataset location
            
        Returns:
            True if dataset was created or already exists
        """
        try:
            dataset_ref = self.client.dataset(dataset_id)
            
            try:
                self.client.get_dataset(dataset_ref)
                logger.info(f"Dataset already exists: {dataset_id}")
                return True
            except NotFound:
                # Dataset doesn't exist, create it
                dataset = bigquery.Dataset(dataset_ref)
                dataset.location = location
                
                dataset = self.client.create_dataset(dataset)
                logger.info(f"Created dataset: {dataset_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error creating dataset: {e}")
            raise
    
    def test_connection(self) -> bool:
        """Test BigQuery connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Simple query to test connection
            query = "SELECT 1 as test_value"
            job = self.client.query(query)
            result = job.result()
            
            # Check if we got a result
            for row in result:
                if row.test_value == 1:
                    logger.info("BigQuery connection test successful")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"BigQuery connection test failed: {e}")
            return False
    
    def get_connection_info(self) -> Dict:
        """Get current connection configuration info.
        
        Returns:
            Dictionary with connection details
        """
        return {
            'project_id': self.config['project_id'],
            'client_active': self.client is not None
        }
    
    def close(self):
        """Close BigQuery client connection."""
        if self.client:
            self.client.close()
            self.client = None
            logger.info("BigQuery client connection closed")