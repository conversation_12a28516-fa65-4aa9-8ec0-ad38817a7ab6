"""PostgreSQL Database Connector for Marine Risk Reports with Connection Pooling."""

import pandas as pd
import psycopg2
from psycopg2 import pool
from sqlalchemy import create_engine, text
import logging
from typing import Dict, Optional
from flask import current_app

logger = logging.getLogger(__name__)


class DatabaseConnector:
    """Handles PostgreSQL database connections and queries using connection pooling."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize database connector.
        
        Args:
            config: Optional config dict, uses Flask config if not provided
        """
        self.config = config or self._get_flask_config()
        self.engine = None
        self.conn_pool = None
        # Don't create connections during initialization - do it lazily
    
    def _get_flask_config(self) -> Dict:
        """Get database configuration from Flask app config.
        
        Returns:
            Database configuration dictionary
        """
        import os
        
        # Always use environment variables directly to ensure consistency
        config = {
            'host': os.getenv('POSTGRES_HOST', os.getenv('AURORA_DB_HOST', 'localhost')),
            'port': int(os.getenv('POSTGRES_PORT', os.getenv('AURORA_DB_PORT', '5432'))),
            'username': os.getenv('POSTGRES_USERNAME', os.getenv('AURORA_USERNAME', 'postgres')),
            'password': os.getenv('POSTGRES_PASSWORD', os.getenv('AURORA_PASSWORD', 'password')),
            'database': os.getenv('POSTGRES_DB', os.getenv('DB_NAME', 'questmarine'))
        }
        
        # Try to get from Flask config if available, but use env vars as fallback
        try:
            flask_config = current_app.config.get('POSTGRES_CONFIG', {})
            # Only override if Flask config has the values
            for key, value in flask_config.items():
                if value is not None:
                    config[key] = value
        except (RuntimeError, KeyError):
            # Flask context not available, use environment variables
            pass
        
        return config
    
    def _create_connection_pool(self):
        """Create psycopg2 connection pool."""
        try:
            self.conn_pool = psycopg2.pool.SimpleConnectionPool(
                1, 10,  # min=1, max=10 connections
                user=self.config['username'],
                password=self.config['password'],
                host=self.config['host'],
                port=self.config['port'],
                database=self.config['database']
            )
            logger.info(f"Created connection pool to {self.config['host']}:{self.config['port']}/{self.config['database']}")
            
        except (Exception, psycopg2.DatabaseError) as error:
            logger.error(f"Error creating connection pool: {error}")
            raise
    
    def _create_sqlalchemy_engine(self):
        """Create SQLAlchemy engine for pandas operations."""
        try:
            db_url = (f"postgresql://{self.config['username']}:{self.config['password']}"
                     f"@{self.config['host']}:{self.config['port']}/{self.config['database']}")
            
            self.engine = create_engine(
                db_url,
                pool_size=5,
                max_overflow=10,
                pool_recycle=3600,
                pool_pre_ping=True
            )
            
            logger.info("PostgreSQL SQLAlchemy engine created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create SQLAlchemy engine: {e}")
            raise
    
    def _ensure_connections(self):
        """Ensure database connections are initialized."""
        if self.engine is None:
            self._create_sqlalchemy_engine()
        if self.conn_pool is None:
            self._create_connection_pool()
    
    def get_connection(self):
        """Get a connection from the pool.
        
        Returns:
            Database connection
        """
        try:
            self._ensure_connections()
            if self.conn_pool:
                conn = self.conn_pool.getconn()
                return conn
            else:
                raise RuntimeError("Connection pool not initialized")
        except (Exception, psycopg2.DatabaseError) as error:
            logger.error(f"Error getting connection from pool: {error}")
            raise
    
    def release_connection(self, conn):
        """Return a connection to the pool.
        
        Args:
            conn: Database connection to release
        """
        if self.conn_pool and conn:
            self.conn_pool.putconn(conn)
    
    def execute_query(self, query: str, params: Optional[Dict] = None) -> pd.DataFrame:
        """Execute a SQL query and return results as DataFrame.
        
        Args:
            query: SQL query string
            params: Optional parameters for the query
            
        Returns:
            DataFrame with query results
        """
        try:
            self._ensure_connections()
            
            if params:
                # Use SQLAlchemy for parameterized queries
                with self.engine.connect() as conn:
                    # Use SQLAlchemy's text() with bound parameters
                    sql_text = text(query)
                    result = pd.read_sql(sql_text, conn, params=params)
            else:
                # Use connection pool for simple queries
                conn = self.get_connection()
                try:
                    result = pd.read_sql(query, conn)
                finally:
                    self.release_connection(conn)
            
            logger.debug(f"Query executed successfully, returned {len(result)} rows")
            return result
            
        except Exception as e:
            logger.error(f"Error executing query: {e}")
            logger.debug(f"Query: {query}")
            if params:
                logger.debug(f"Params: {params}")
            raise
    
    def execute_raw_query(self, query: str, params: Optional[tuple] = None):
        """Execute a raw SQL query using psycopg2 directly.
        
        Args:
            query: SQL query string
            params: Optional parameters tuple
            
        Returns:
            Query results or row count
        """
        conn = self.get_connection()
        try:
            with conn.cursor() as cursor:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if cursor.description:  # SELECT query
                    columns = [desc[0] for desc in cursor.description]
                    rows = cursor.fetchall()
                    return pd.DataFrame(rows, columns=columns)
                else:  # INSERT/UPDATE/DELETE query
                    conn.commit()
                    return cursor.rowcount
                    
        except Exception as e:
            conn.rollback()
            logger.error(f"Error executing raw query: {e}")
            raise
        finally:
            self.release_connection(conn)
    
    def test_connection(self) -> bool:
        """Test database connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            conn = self.get_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
            self.release_connection(conn)
            logger.info("Database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_connection_info(self) -> Dict:
        """Get current connection configuration info.
        
        Returns:
            Dictionary with connection details
        """
        return {
            'host': self.config['host'],
            'port': self.config['port'],
            'database': self.config['database'],
            'username': self.config['username'],
            'pool_active': self.conn_pool is not None
        }
    
    def close(self):
        """Close all database connections."""
        if self.conn_pool:
            self.conn_pool.closeall()
            self.conn_pool = None
            logger.info("All database connections closed")
        
        if self.engine:
            self.engine.dispose()
            self.engine = None