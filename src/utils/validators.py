"""Input validation utilities for Marine HMM Reports API."""

import re
import uuid
from typing import Any, Dict, List, Optional


def validate_tenant_id(tenant_id: str) -> bool:
    """Validate tenant ID format.
    
    Args:
        tenant_id: Tenant identifier to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not tenant_id or not isinstance(tenant_id, str):
        return False
    
    # Tenant ID should be alphanumeric with optional hyphens/underscores
    # Length between 1 and 50 characters
    pattern = r'^[a-zA-Z0-9_-]{1,50}$'
    return bool(re.match(pattern, tenant_id))


def validate_job_id(job_id: str) -> bool:
    """Validate job ID format (UUID).
    
    Args:
        job_id: Job identifier to validate
        
    Returns:
        True if valid UUID, False otherwise
    """
    if not job_id or not isinstance(job_id, str):
        return False
    
    try:
        uuid.UUID(job_id)
        return True
    except ValueError:
        return False


def validate_email(email: str) -> bool:
    """Validate email address format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid email format, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    # Basic email validation pattern
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_job_status(status: str) -> bool:
    """Validate job status value.
    
    Args:
        status: Job status to validate
        
    Returns:
        True if valid status, False otherwise
    """
    valid_statuses = {'pending', 'running', 'completed', 'failed', 'cancelled'}
    return status in valid_statuses


def validate_pagination_params(limit: Any, offset: Any = None) -> Dict[str, Any]:
    """Validate and normalize pagination parameters.
    
    Args:
        limit: Maximum number of items to return
        offset: Number of items to skip (optional)
        
    Returns:
        Dictionary with validated parameters
        
    Raises:
        ValueError: If parameters are invalid
    """
    # Validate limit
    try:
        limit = int(limit) if limit is not None else 50
    except (ValueError, TypeError):
        raise ValueError("Limit must be a valid integer")
    
    if limit < 1 or limit > 1000:
        raise ValueError("Limit must be between 1 and 1000")
    
    # Validate offset
    if offset is not None:
        try:
            offset = int(offset)
        except (ValueError, TypeError):
            raise ValueError("Offset must be a valid integer")
        
        if offset < 0:
            raise ValueError("Offset must be non-negative")
    else:
        offset = 0
    
    return {'limit': limit, 'offset': offset}


def validate_report_generation_request(data: Dict) -> Dict[str, Any]:
    """Validate report generation request data.
    
    Args:
        data: Request data dictionary
        
    Returns:
        Dictionary with validated parameters
        
    Raises:
        ValueError: If validation fails
    """
    if not isinstance(data, dict):
        raise ValueError("Request data must be a JSON object")
    
    # Validate tenant_id (required)
    tenant_id = data.get('tenant_id')
    if not tenant_id:
        raise ValueError("tenant_id is required")
    
    if not validate_tenant_id(tenant_id):
        raise ValueError("Invalid tenant_id format")
    
    # Validate async flag (optional)
    async_mode = data.get('async', True)
    if not isinstance(async_mode, bool):
        raise ValueError("async must be a boolean value")
    
    # Validate user_id (optional)
    user_id = data.get('user_id')
    if user_id is not None and not isinstance(user_id, str):
        raise ValueError("user_id must be a string")
    
    return {
        'tenant_id': tenant_id,
        'async': async_mode,
        'user_id': user_id
    }


def validate_scheduler_config(config: Dict) -> Dict[str, Any]:
    """Validate scheduler configuration parameters.
    
    Args:
        config: Scheduler configuration dictionary
        
    Returns:
        Dictionary with validated parameters
        
    Raises:
        ValueError: If validation fails
    """
    if not isinstance(config, dict):
        raise ValueError("Configuration must be a dictionary")
    
    validated = {}
    
    # Validate day (1-31)
    day = config.get('day')
    if day is not None:
        try:
            day = int(day)
        except (ValueError, TypeError):
            raise ValueError("Day must be a valid integer")
        
        if not 1 <= day <= 31:
            raise ValueError("Day must be between 1 and 31")
        
        validated['day'] = day
    
    # Validate hour (0-23)
    hour = config.get('hour')
    if hour is not None:
        try:
            hour = int(hour)
        except (ValueError, TypeError):
            raise ValueError("Hour must be a valid integer")
        
        if not 0 <= hour <= 23:
            raise ValueError("Hour must be between 0 and 23")
        
        validated['hour'] = hour
    
    # Validate minute (0-59)
    minute = config.get('minute')
    if minute is not None:
        try:
            minute = int(minute)
        except (ValueError, TypeError):
            raise ValueError("Minute must be a valid integer")
        
        if not 0 <= minute <= 59:
            raise ValueError("Minute must be between 0 and 59")
        
        validated['minute'] = minute
    
    # Validate timezone
    timezone = config.get('timezone')
    if timezone is not None:
        if not isinstance(timezone, str):
            raise ValueError("Timezone must be a string")
        
        # Basic timezone validation (could be enhanced)
        valid_timezones = ['UTC', 'GMT', 'Europe/London', 'America/New_York', 'America/Los_Angeles']
        if timezone not in valid_timezones:
            # Allow any timezone string, but log a warning
            pass
        
        validated['timezone'] = timezone
    
    # Validate enabled flag
    enabled = config.get('enabled')
    if enabled is not None:
        if not isinstance(enabled, bool):
            raise ValueError("Enabled must be a boolean value")
        
        validated['enabled'] = enabled
    
    return validated


def validate_file_path(file_path: str) -> bool:
    """Validate file path format.
    
    Args:
        file_path: File path to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not file_path or not isinstance(file_path, str):
        return False
    
    # Basic file path validation
    # Prevent directory traversal attacks
    if '..' in file_path or file_path.startswith('/'):
        return False
    
    # Check for valid characters
    pattern = r'^[a-zA-Z0-9._/-]+$'
    return bool(re.match(pattern, file_path))


def validate_gcs_path(gcs_path: str) -> bool:
    """Validate Google Cloud Storage path format.
    
    Args:
        gcs_path: GCS path to validate
        
    Returns:
        True if valid GCS path, False otherwise
    """
    if not gcs_path or not isinstance(gcs_path, str):
        return False
    
    # GCS path should start with gs://
    if not gcs_path.startswith('gs://'):
        return False
    
    # Basic validation of bucket and object name
    pattern = r'^gs://[a-zA-Z0-9._-]+/[a-zA-Z0-9._/-]+$'
    return bool(re.match(pattern, gcs_path))


def sanitize_filename(filename: str) -> str:
    """Sanitize filename by removing/replacing invalid characters.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    if not filename or not isinstance(filename, str):
        return 'unnamed_file'
    
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = 'unnamed_file'
    
    # Limit length
    if len(sanitized) > 255:
        name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
        max_name_length = 255 - len(ext) - 1 if ext else 255
        sanitized = name[:max_name_length] + ('.' + ext if ext else '')
    
    return sanitized


def validate_content_type(content_type: str, allowed_types: List[str] = None) -> bool:
    """Validate content type.
    
    Args:
        content_type: Content type to validate
        allowed_types: List of allowed content types (optional)
        
    Returns:
        True if valid, False otherwise
    """
    if not content_type or not isinstance(content_type, str):
        return False
    
    # Default allowed types for Excel files
    if allowed_types is None:
        allowed_types = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/octet-stream'
        ]
    
    return content_type in allowed_types