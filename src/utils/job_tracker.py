"""Job tracking and status management for Marine HMM Reports API."""

import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class JobTracker:
    """In-memory job tracking and status management."""
    
    def __init__(self):
        """Initialize job tracker with thread-safe storage."""
        self._jobs = {}
        self._lock = threading.RLock()
        
        # Job status constants
        self.VALID_STATUSES = {'pending', 'running', 'completed', 'failed', 'cancelled'}
    
    def create_job(self, job_id: str, job_data: Dict, status: str = 'pending') -> bool:
        """Create a new job record.
        
        Args:
            job_id: Unique job identifier
            job_data: Initial job data
            status: Initial job status (default: 'pending')
            
        Returns:
            True if job created successfully, False if job already exists
        """
        if status not in self.VALID_STATUSES:
            logger.error(f"Invalid job status: {status}")
            return False
            
        with self._lock:
            if job_id in self._jobs:
                logger.warning(f"Job already exists: {job_id}")
                return False
            
            self._jobs[job_id] = {
                'id': job_id,
                'status': status,
                'created_at': datetime.utcnow(),
                'updated_at': datetime.utcnow(),
                'data': job_data.copy() if job_data else {}
            }
            
            logger.info(f"Job created: {job_id} with status: {status}")
            return True
    
    def update_job_status(self, job_id: str, status: str, additional_data: Dict = None) -> bool:
        """Update job status and additional data.
        
        Args:
            job_id: Job identifier
            status: New job status
            additional_data: Additional data to merge into job record
            
        Returns:
            True if update successful, False if job not found or invalid status
        """
        if status not in self.VALID_STATUSES:
            logger.error(f"Invalid job status: {status}")
            return False
        
        with self._lock:
            if job_id not in self._jobs:
                logger.warning(f"Job not found for status update: {job_id}")
                return False
            
            job = self._jobs[job_id]
            old_status = job['status']
            
            # Update status and timestamp
            job['status'] = status
            job['updated_at'] = datetime.utcnow()
            
            # Merge additional data
            if additional_data:
                job['data'].update(additional_data)
            
            logger.info(f"Job {job_id} status updated: {old_status} -> {status}")
            return True
    
    def get_job_status(self, job_id: str) -> Optional[Dict]:
        """Get job status and information.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Job information dictionary or None if not found
        """
        with self._lock:
            job = self._jobs.get(job_id)
            if job:
                # Return a copy to prevent external modification
                return {
                    'id': job['id'],
                    'status': job['status'],
                    'created_at': job['created_at'].isoformat() + 'Z',
                    'updated_at': job['updated_at'].isoformat() + 'Z',
                    'data': job['data'].copy()
                }
            return None
    
    def list_jobs(self, job_type: str = None, tenant_id: str = None, 
                  status: str = None, limit: int = 50) -> List[Dict]:
        """List jobs with optional filtering.
        
        Args:
            job_type: Filter by job type
            tenant_id: Filter by tenant ID
            status: Filter by job status
            limit: Maximum number of jobs to return
            
        Returns:
            List of job information dictionaries
        """
        with self._lock:
            jobs = []
            
            for job in self._jobs.values():
                # Apply filters
                if job_type and job['data'].get('type') != job_type:
                    continue
                
                if tenant_id and job['data'].get('tenant_id') != tenant_id:
                    continue
                
                if status and job['status'] != status:
                    continue
                
                # Add to results
                jobs.append({
                    'id': job['id'],
                    'status': job['status'],
                    'created_at': job['created_at'].isoformat() + 'Z',
                    'updated_at': job['updated_at'].isoformat() + 'Z',
                    'data': job['data'].copy()
                })
                
                # Check limit
                if len(jobs) >= limit:
                    break
            
            # Sort by creation time (newest first)
            jobs.sort(key=lambda x: x['created_at'], reverse=True)
            
            return jobs
    
    def delete_job(self, job_id: str) -> bool:
        """Delete a job record.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if job deleted successfully, False if not found
        """
        with self._lock:
            if job_id in self._jobs:
                del self._jobs[job_id]
                logger.info(f"Job deleted: {job_id}")
                return True
            else:
                logger.warning(f"Job not found for deletion: {job_id}")
                return False
    
    def cleanup_old_jobs(self, days_old: int = 30) -> int:
        """Clean up old job records.
        
        Args:
            days_old: Remove jobs older than this many days
            
        Returns:
            Number of jobs cleaned up
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        cleaned_count = 0
        
        with self._lock:
            jobs_to_delete = []
            
            for job_id, job in self._jobs.items():
                if job['created_at'] < cutoff_date:
                    jobs_to_delete.append(job_id)
            
            for job_id in jobs_to_delete:
                del self._jobs[job_id]
                cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"Cleaned up {cleaned_count} old jobs (older than {days_old} days)")
        
        return cleaned_count
    
    def get_job_statistics(self) -> Dict:
        """Get job statistics.
        
        Returns:
            Dictionary with job statistics
        """
        with self._lock:
            stats = {
                'total_jobs': len(self._jobs),
                'status_counts': {},
                'type_counts': {},
                'tenant_counts': {}
            }
            
            for job in self._jobs.values():
                # Count by status
                status = job['status']
                stats['status_counts'][status] = stats['status_counts'].get(status, 0) + 1
                
                # Count by type
                job_type = job['data'].get('type', 'unknown')
                stats['type_counts'][job_type] = stats['type_counts'].get(job_type, 0) + 1
                
                # Count by tenant
                tenant_id = job['data'].get('tenant_id', 'unknown')
                stats['tenant_counts'][tenant_id] = stats['tenant_counts'].get(tenant_id, 0) + 1
            
            return stats
    
    def get_running_jobs(self) -> List[Dict]:
        """Get all currently running jobs.
        
        Returns:
            List of running job information dictionaries
        """
        return self.list_jobs(status='running', limit=1000)
    
    def get_failed_jobs(self, hours: int = 24) -> List[Dict]:
        """Get jobs that failed within the specified time period.
        
        Args:
            hours: Look for failed jobs within this many hours
            
        Returns:
            List of failed job information dictionaries
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        with self._lock:
            failed_jobs = []
            
            for job in self._jobs.values():
                if (job['status'] == 'failed' and 
                    job['updated_at'] >= cutoff_time):
                    failed_jobs.append({
                        'id': job['id'],
                        'status': job['status'],
                        'created_at': job['created_at'].isoformat() + 'Z',
                        'updated_at': job['updated_at'].isoformat() + 'Z',
                        'data': job['data'].copy()
                    })
            
            # Sort by update time (newest first)
            failed_jobs.sort(key=lambda x: x['updated_at'], reverse=True)
            
            return failed_jobs
    
    def update_job_data(self, job_id: str, data_updates: Dict) -> bool:
        """Update job data without changing status.
        
        Args:
            job_id: Job identifier
            data_updates: Data updates to apply
            
        Returns:
            True if update successful, False if job not found
        """
        with self._lock:
            if job_id not in self._jobs:
                logger.warning(f"Job not found for data update: {job_id}")
                return False
            
            job = self._jobs[job_id]
            job['data'].update(data_updates)
            job['updated_at'] = datetime.utcnow()
            
            logger.debug(f"Job {job_id} data updated")
            return True
    
    def job_exists(self, job_id: str) -> bool:
        """Check if a job exists.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if job exists, False otherwise
        """
        with self._lock:
            return job_id in self._jobs
    
    def get_job_count(self) -> int:
        """Get total number of jobs.
        
        Returns:
            Total job count
        """
        with self._lock:
            return len(self._jobs)
    
    def clear_all_jobs(self) -> int:
        """Clear all job records (use with caution).
        
        Returns:
            Number of jobs cleared
        """
        with self._lock:
            count = len(self._jobs)
            self._jobs.clear()
            logger.warning(f"Cleared all {count} job records")
            return count