"""Logging configuration utilities for Marine HMM Reports API."""

import logging
import logging.handlers
import sys
import uuid
from datetime import datetime
from typing import Dict, Optional
from flask import request, g, has_request_context


class RequestContextFilter(logging.Filter):
    """Logging filter to add request context information."""
    
    def filter(self, record):
        """Add request context to log record.
        
        Args:
            record: Log record to modify
            
        Returns:
            True to include the record in output
        """
        if has_request_context():
            # Add request ID
            if not hasattr(g, 'request_id'):
                g.request_id = str(uuid.uuid4())[:8]
            record.request_id = g.request_id
            
            # Add request information
            record.method = request.method
            record.url = request.url
            record.remote_addr = request.remote_addr
            record.user_agent = request.headers.get('User-Agent', 'Unknown')
        else:
            # Default values when not in request context
            record.request_id = 'N/A'
            record.method = 'N/A'
            record.url = 'N/A'
            record.remote_addr = 'N/A'
            record.user_agent = 'N/A'
        
        return True


class StructuredFormatter(logging.Formatter):
    """Structured logging formatter for JSON-like output."""
    
    def __init__(self, include_request_context=True):
        """Initialize structured formatter.
        
        Args:
            include_request_context: Whether to include request context
        """
        super().__init__()
        self.include_request_context = include_request_context
    
    def format(self, record):
        """Format log record as structured data.
        
        Args:
            record: Log record to format
            
        Returns:
            Formatted log string
        """
        # Base log data
        log_data = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add request context if available and enabled
        if self.include_request_context and hasattr(record, 'request_id'):
            log_data.update({
                'request_id': record.request_id,
                'method': record.method,
                'url': record.url,
                'remote_addr': record.remote_addr,
                'user_agent': record.user_agent
            })
        
        # Add exception information if present
        if record.exc_info:
            log_data['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields from record
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info', 'request_id', 'method', 'url',
                          'remote_addr', 'user_agent']:
                log_data[key] = value
        
        # Format as key=value pairs for easier parsing
        formatted_parts = []
        for key, value in log_data.items():
            if isinstance(value, str) and ' ' in value:
                formatted_parts.append(f'{key}="{value}"')
            else:
                formatted_parts.append(f'{key}={value}')
        
        return ' '.join(formatted_parts)


def setup_logging(app, log_level: str = None, log_format: str = None,
                 enable_request_context: bool = True,
                 enable_file_logging: bool = False,
                 log_file_path: str = None) -> None:
    """Setup comprehensive logging configuration.
    
    Args:
        app: Flask application instance
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_format: Log format string
        enable_request_context: Whether to enable request context logging
        enable_file_logging: Whether to enable file logging
        log_file_path: Path to log file (if file logging enabled)
    """
    # Get configuration from app config or use defaults
    log_level = log_level or app.config.get('LOG_LEVEL', 'INFO')
    log_format = log_format or app.config.get('LOG_FORMAT', 
                                             '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Clear existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)
    
    # Setup formatter based on environment
    if app.config.get('FLASK_ENV') == 'production':
        # Use structured logging in production
        formatter = StructuredFormatter(include_request_context=enable_request_context)
    else:
        # Use standard formatting in development
        formatter = logging.Formatter(log_format)
    
    console_handler.setFormatter(formatter)
    
    # Add request context filter if enabled
    if enable_request_context:
        request_filter = RequestContextFilter()
        console_handler.addFilter(request_filter)
    
    # Add console handler to root logger
    root_logger.addHandler(console_handler)
    root_logger.setLevel(numeric_level)
    
    # Setup file logging if enabled
    if enable_file_logging:
        log_file_path = log_file_path or 'logs/marine_hmm_api.log'
        
        # Create rotating file handler
        try:
            import os
            os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
            
            file_handler = logging.handlers.RotatingFileHandler(
                log_file_path,
                maxBytes=10 * 1024 * 1024,  # 10MB
                backupCount=5
            )
            file_handler.setLevel(numeric_level)
            file_handler.setFormatter(formatter)
            
            if enable_request_context:
                file_handler.addFilter(request_filter)
            
            root_logger.addHandler(file_handler)
            
        except Exception as e:
            app.logger.warning(f"Failed to setup file logging: {e}")
    
    # Configure specific loggers
    configure_third_party_loggers(numeric_level)
    
    # Set Flask app logger level
    app.logger.setLevel(numeric_level)
    
    app.logger.info(f"Logging configured - Level: {log_level}, "
                   f"Request Context: {enable_request_context}, "
                   f"File Logging: {enable_file_logging}")


def configure_third_party_loggers(log_level: int) -> None:
    """Configure third-party library loggers.
    
    Args:
        log_level: Numeric log level
    """
    # Suppress verbose third-party logs in production
    if log_level >= logging.WARNING:
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('google').setLevel(logging.WARNING)
        logging.getLogger('google.cloud').setLevel(logging.WARNING)
        logging.getLogger('google.auth').setLevel(logging.WARNING)
        logging.getLogger('apscheduler').setLevel(logging.WARNING)
    else:
        # In development, allow more verbose logging
        logging.getLogger('werkzeug').setLevel(logging.INFO)
        logging.getLogger('apscheduler').setLevel(logging.INFO)


def get_correlation_id() -> str:
    """Get or create correlation ID for current request.
    
    Returns:
        Correlation ID string
    """
    if has_request_context():
        if not hasattr(g, 'request_id'):
            g.request_id = str(uuid.uuid4())[:8]
        return g.request_id
    else:
        return str(uuid.uuid4())[:8]


def log_request_start():
    """Log request start information."""
    if has_request_context():
        correlation_id = get_correlation_id()
        logger = logging.getLogger('marine_hmm_api.request')
        logger.info(f"Request started - {request.method} {request.path}",
                   extra={'correlation_id': correlation_id})


def log_request_end(response):
    """Log request end information.
    
    Args:
        response: Flask response object
        
    Returns:
        Response object (unchanged)
    """
    if has_request_context():
        correlation_id = get_correlation_id()
        logger = logging.getLogger('marine_hmm_api.request')
        logger.info(f"Request completed - Status: {response.status_code}",
                   extra={'correlation_id': correlation_id,
                         'status_code': response.status_code})
    return response


def setup_request_logging(app):
    """Setup request/response logging middleware.
    
    Args:
        app: Flask application instance
    """
    @app.before_request
    def before_request():
        """Log request start."""
        log_request_start()
    
    @app.after_request
    def after_request(response):
        """Log request end."""
        return log_request_end(response)
    
    app.logger.info("Request logging middleware configured")


class LoggerMixin:
    """Mixin class to add logging capabilities to other classes."""
    
    @property
    def logger(self):
        """Get logger instance for this class."""
        return logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
    
    def log_method_call(self, method_name: str, **kwargs):
        """Log method call with parameters.
        
        Args:
            method_name: Name of the method being called
            **kwargs: Method parameters to log
        """
        self.logger.debug(f"Calling {method_name}", extra=kwargs)
    
    def log_method_result(self, method_name: str, result: any = None, **kwargs):
        """Log method result.
        
        Args:
            method_name: Name of the method
            result: Method result (optional)
            **kwargs: Additional context to log
        """
        extra_data = kwargs.copy()
        if result is not None:
            extra_data['result_type'] = type(result).__name__
        
        self.logger.debug(f"Completed {method_name}", extra=extra_data)


def create_audit_logger(name: str = 'audit') -> logging.Logger:
    """Create a dedicated audit logger.
    
    Args:
        name: Logger name
        
    Returns:
        Configured audit logger
    """
    audit_logger = logging.getLogger(f'marine_hmm_api.{name}')
    
    # Create audit-specific formatter
    audit_formatter = logging.Formatter(
        '%(asctime)s - AUDIT - %(levelname)s - %(message)s'
    )
    
    # Create separate handler for audit logs
    audit_handler = logging.StreamHandler(sys.stdout)
    audit_handler.setFormatter(audit_formatter)
    audit_logger.addHandler(audit_handler)
    audit_logger.setLevel(logging.INFO)
    
    # Prevent propagation to avoid duplicate logs
    audit_logger.propagate = False
    
    return audit_logger