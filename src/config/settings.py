"""Environment-based configuration management for Marine HMM Reports API."""

import os
from datetime import datetime


def _parse_tenant_ids(tenant_ids_str):
    """Parse comma-separated tenant IDs from environment variable.
    
    Args:
        tenant_ids_str: Comma-separated string of tenant IDs
        
    Returns:
        List of tenant IDs
    """
    if not tenant_ids_str:
        return []
    return [tenant.strip() for tenant in tenant_ids_str.split(',') if tenant.strip()]


class BaseConfig:
    """Base configuration class with common settings."""
    
    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = False
    TESTING = False
    
    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # CORS Configuration
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', '*').split(',')
    
    # Scheduler Configuration (UTC times)
    scheduler_time = os.getenv('SCHEDULER_TIME', '7:8:0')
    try:
        day, hour, minute = map(int, scheduler_time.split(':'))
    except ValueError:
        day, hour, minute = 7, 8, 0
    
    SCHEDULER_CONFIG = {
        'day': day,           # Day of every month
        'hour': hour,         # Hour in UTC
        'minute': minute,     # Minute
        'timezone': os.getenv('SCHEDULER_TIMEZONE', 'UTC'),
        'enabled': os.getenv('SCHEDULER_ENABLED', 'true').lower() == 'true'
    }
    
    # Database Configuration
    POSTGRES_CONFIG = {
        'host': os.getenv('POSTGRES_HOST', os.getenv('AURORA_DB_HOST', 'localhost')),
        'port': int(os.getenv('POSTGRES_PORT', os.getenv('AURORA_DB_PORT', '5432'))),
        'username': os.getenv('POSTGRES_USERNAME', os.getenv('AURORA_USERNAME', 'postgres')),
        'password': os.getenv('POSTGRES_PASSWORD', os.getenv('AURORA_PASSWORD', 'password')),
        'database': os.getenv('POSTGRES_DB', os.getenv('DB_NAME', 'questmarine'))
    }
    
    # GCP Configuration
    GCP_CONFIG = {
        'project_id': os.getenv('PROJECT_ID', 'prj-nonprod-eng-svc-01'),
        'bucket_name': os.getenv('HMM_REPORTS_BUCKET_NAME', 'hull-nonprod-static-content'),
        'pubsub_topic': os.getenv('PUBSUB_TOPIC', 'marine-reports')
    }
    
    # Multi-tenant Configuration
    TENANT_CONFIG = {
        'enabled_tenant_ids': _parse_tenant_ids(os.getenv('ENABLED_FOR_TENANT_IDS', '')),
        'tenant_users_config': os.getenv('TENANT_USERS_CONFIG', '')
    }
    
    # Utility function for timestamps
    @staticmethod
    def CURRENT_TIME_FUNC():
        """Get current UTC timestamp in ISO format."""
        return datetime.utcnow().isoformat() + 'Z'


class DevelopmentConfig(BaseConfig):
    """Development environment configuration."""
    
    DEBUG = True
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')
    
    # Override scheduler for development (disabled by default)
    SCHEDULER_CONFIG = BaseConfig.SCHEDULER_CONFIG.copy()
    SCHEDULER_CONFIG['enabled'] = os.getenv('SCHEDULER_ENABLED', 'false').lower() == 'true'


class StagingConfig(BaseConfig):
    """Staging environment configuration."""
    
    DEBUG = False
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')


class ProductionConfig(BaseConfig):
    """Production environment configuration."""
    
    DEBUG = False
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'WARNING')
    
    # Production-specific security settings
    SECRET_KEY = os.getenv('SECRET_KEY')  # Must be set in production
    
    # Stricter CORS in production
    CORS_ORIGINS = os.getenv('CORS_ORIGINS', '').split(',') if os.getenv('CORS_ORIGINS') else []


def get_config_class(config_name):
    """Get configuration class by name.
    
    Args:
        config_name: Configuration name (dev, staging, prod)
        
    Returns:
        Configuration class
    """
    config_map = {
        'development': DevelopmentConfig,
        'dev': DevelopmentConfig,
        'staging': StagingConfig,
        'production': ProductionConfig,
        'prod': ProductionConfig
    }
    
    return config_map.get(config_name.lower(), ProductionConfig)


def get_users_for_tenant(tenant_id, tenant_users_config=None):
    """Get users list for specific tenant.
    
    Args:
        tenant_id: Tenant identifier
        tenant_users_config: Tenant users configuration string
        
    Returns:
        List of user dictionaries with name and email
    """
    users = []
    config = tenant_users_config or BaseConfig.TENANT_CONFIG['tenant_users_config']
    
    if config:
        for tenant_config in config.split(';'):
            if '|' in tenant_config:
                tenant_part, users_part = tenant_config.strip().split('|', 1)

                # Handle both old format (tenant_id|users) and new format (tenant_id:tenant_name|users)
                if ':' in tenant_part:
                    config_tenant_id, _ = tenant_part.split(':', 1)
                else:
                    config_tenant_id = tenant_part

                if config_tenant_id.strip() == tenant_id:
                    for user_entry in users_part.split(','):
                        if ':' in user_entry:
                            name, email = user_entry.strip().split(':', 1)
                            users.append({'name': name.strip(), 'email': email.strip()})
                    break
    return users


def get_tenant_name(tenant_id, tenant_users_config=None):
    """Get tenant name for specific tenant ID.

    Args:
        tenant_id: Tenant identifier
        tenant_users_config: Tenant users configuration string

    Returns:
        Tenant name if found, otherwise returns tenant_id
    """
    config = tenant_users_config or BaseConfig.TENANT_CONFIG['tenant_users_config']

    if config:
        for tenant_config in config.split(';'):
            if '|' in tenant_config:
                tenant_part, _ = tenant_config.strip().split('|', 1)

                # Check if new format with tenant name (tenant_id:tenant_name)
                if ':' in tenant_part:
                    config_tenant_id, tenant_name = tenant_part.split(':', 1)
                    if config_tenant_id.strip() == tenant_id:
                        return tenant_name.strip()

    # Return tenant_id if no name found (backward compatibility)
    return tenant_id