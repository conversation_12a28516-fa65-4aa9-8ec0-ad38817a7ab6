"""Scheduler configuration for Marine HMM Reports API."""

import os
import logging
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)


class SchedulerConfig:
    """Scheduler configuration management."""
    
    @staticmethod
    def get_scheduler_config() -> Dict[str, Any]:
        """Get scheduler configuration from environment variables.
        
        Returns:
            Dictionary containing scheduler configuration
        """
        # Parse SCHEDULER_TIME format: "day:hour:minute" (e.g., "15:10:30")
        scheduler_time = os.getenv('SCHEDULER_TIME', '7:8:0')
        try:
            day, hour, minute = map(int, scheduler_time.split(':'))
        except ValueError:
            logger.warning(f"Invalid SCHEDULER_TIME format: {scheduler_time}, using defaults")
            day, hour, minute = 7, 8, 0
        
        config = {
            'day': day,
            'hour': hour,
            'minute': minute,
            'timezone': os.getenv('SCHEDULER_TIMEZONE', 'UTC'),
            'enabled': os.getenv('SCHEDULER_ENABLED', 'true').lower() == 'true'
        }
        
        # Validate configuration
        SchedulerConfig.validate_config(config)
        
        return config
    
    @staticmethod
    def validate_config(config: Dict[str, Any]) -> None:
        """Validate scheduler configuration parameters.
        
        Args:
            config: Scheduler configuration dictionary
            
        Raises:
            ValueError: If configuration is invalid
        """
        # Validate day (1-31)
        if not 1 <= config['day'] <= 31:
            raise ValueError(f"Invalid scheduler day: {config['day']}. Must be between 1-31")
        
        # Validate hour (0-23)
        if not 0 <= config['hour'] <= 23:
            raise ValueError(f"Invalid scheduler hour: {config['hour']}. Must be between 0-23")
        
        # Validate minute (0-59)
        if not 0 <= config['minute'] <= 59:
            raise ValueError(f"Invalid scheduler minute: {config['minute']}. Must be between 0-59")
        
        # Validate timezone
        valid_timezones = ['UTC', 'GMT', 'Europe/London']
        if config['timezone'] not in valid_timezones:
            logger.warning(f"Timezone {config['timezone']} not in recommended list: {valid_timezones}")
    
    @staticmethod
    def get_cron_expression(config: Dict[str, Any]) -> str:
        """Generate cron expression from scheduler configuration.
        
        Args:
            config: Scheduler configuration dictionary
            
        Returns:
            Cron expression string
        """
        return f"{config['minute']} {config['hour']} {config['day']} * *"
    
    @staticmethod
    def log_scheduler_config(config: Dict[str, Any]) -> None:
        """Log current scheduler configuration.
        
        Args:
            config: Scheduler configuration dictionary
        """
        if config['enabled']:
            logger.info(f"Scheduler enabled: Monthly reports on day {config['day']} "
                       f"at {config['hour']:02d}:{config['minute']:02d} {config['timezone']}")
        else:
            logger.info("Scheduler disabled")
        
        logger.debug(f"Full scheduler config: {config}")


def get_default_scheduler_config() -> Dict[str, Any]:
    """Get default scheduler configuration.
    
    Returns:
        Default scheduler configuration dictionary
    """
    return {
        'day': 7,           # 7th of every month
        'hour': 8,          # 8 AM UTC (9 AM UK time)
        'minute': 0,
        'timezone': 'UTC',
        'enabled': True
    }


def is_business_day_adjustment_needed() -> bool:
    """Check if business day adjustment is needed.
    
    This function can be extended to handle business day logic
    when the 7th falls on a weekend.
    
    Returns:
        Boolean indicating if adjustment is needed
    """
    # For now, return False - can be extended later
    return False